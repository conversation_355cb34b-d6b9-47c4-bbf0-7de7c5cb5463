<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时间戳转换器 - 稳定版本</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 30px;
            margin: 20px 0;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        .current-time-display {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        
        .time-item {
            background: rgba(0, 0, 0, 0.2);
            padding: 20px;
            border-radius: 15px;
            text-align: center;
        }
        
        .time-value {
            font-size: 1.5rem;
            font-weight: bold;
            font-family: 'Courier New', monospace;
        }
        
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }
        
        .input-group {
            margin: 20px 0;
        }
        
        .input-group label {
            display: block;
            margin-bottom: 10px;
            font-weight: 500;
        }
        
        .input-group input {
            width: 100%;
            padding: 15px;
            border: none;
            border-radius: 12px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            font-size: 16px;
        }
        
        .input-group input::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }
        
        .result {
            background: rgba(0, 0, 0, 0.3);
            padding: 20px;
            border-radius: 12px;
            margin-top: 15px;
            font-family: 'Courier New', monospace;
            font-size: 1.1rem;
            min-height: 60px;
            display: flex;
            align-items: center;
        }
        
        .btn {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        
        .footer {
            text-align: center;
            margin-top: 50px;
            opacity: 0.8;
        }
        
        @media (max-width: 768px) {
            .grid, .current-time-display {
                grid-template-columns: 1fr;
            }
            .header h1 {
                font-size: 2rem;
            }
        }
        
        .success { color: #4CAF50; }
        .error { color: #ff6b6b; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⏰ 时间戳转换器</h1>
            <p>Unix时间戳与日期时间双向转换工具</p>
        </div>
        
        <div class="card">
            <h2 style="text-align: center; margin-bottom: 20px;">🕐 当前时间</h2>
            <div class="current-time-display">
                <div class="time-item">
                    <div>Unix 时间戳</div>
                    <div class="time-value" id="currentTimestamp">0</div>
                </div>
                <div class="time-item">
                    <div>本地时间</div>
                    <div class="time-value" id="currentTime">--</div>
                </div>
            </div>
            <div style="text-align: center;">
                <button class="btn" onclick="updateCurrentTime()">🔄 立即更新</button>
            </div>
        </div>

        <div class="grid">
            <div class="card">
                <h2>🔢 时间戳转日期</h2>
                <div class="input-group">
                    <label for="timestampInput">输入Unix时间戳：</label>
                    <input type="number" id="timestampInput" placeholder="例如：1640995200" oninput="convertTimestamp()">
                </div>
                <div class="result" id="timestampResult">请输入时间戳进行转换</div>
            </div>

            <div class="card">
                <h2>📅 日期转时间戳</h2>
                <div class="input-group">
                    <label for="dateInput">选择日期和时间：</label>
                    <input type="datetime-local" id="dateInput" oninput="convertDate()">
                </div>
                <div class="result" id="dateResult">请选择日期时间进行转换</div>
            </div>
        </div>

        <div class="footer">
            <p>© 2024 时间戳转换器 | 免费在线工具</p>
            <p>🚀 纯JavaScript实现，无外部依赖，快速稳定</p>
        </div>
    </div>

    <script>
        console.log('🚀 时间戳转换器稳定版本启动中...');
        
        function formatDateTime(date) {
            try {
                return date.toLocaleString('zh-CN', {
                    timeZone: 'Asia/Shanghai',
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit',
                    weekday: 'long'
                });
            } catch (error) {
                return date.getFullYear() + '-' + 
                       String(date.getMonth() + 1).padStart(2, '0') + '-' + 
                       String(date.getDate()).padStart(2, '0') + ' ' +
                       String(date.getHours()).padStart(2, '0') + ':' + 
                       String(date.getMinutes()).padStart(2, '0') + ':' + 
                       String(date.getSeconds()).padStart(2, '0');
            }
        }

        function updateCurrentTime() {
            try {
                const now = Math.floor(Date.now() / 1000);
                const nowDate = new Date();
                
                document.getElementById('currentTimestamp').textContent = now;
                document.getElementById('currentTime').textContent = formatDateTime(nowDate);
            } catch (error) {
                console.error('更新当前时间失败:', error);
            }
        }

        function convertTimestamp() {
            const input = document.getElementById('timestampInput');
            const result = document.getElementById('timestampResult');
            const timestamp = parseInt(input.value);
            
            if (!input.value.trim()) {
                result.textContent = '请输入时间戳进行转换';
                result.className = 'result';
                return;
            }
            
            if (isNaN(timestamp) || timestamp < 0) {
                result.textContent = '❌ 请输入有效的时间戳';
                result.className = 'result error';
                return;
            }
            
            try {
                const date = new Date(timestamp * 1000);
                if (isNaN(date.getTime())) {
                    result.textContent = '❌ 无效的时间戳';
                    result.className = 'result error';
                    return;
                }
                
                result.textContent = '✅ ' + formatDateTime(date);
                result.className = 'result success';
            } catch (error) {
                result.textContent = '❌ 转换失败: ' + error.message;
                result.className = 'result error';
            }
        }

        function convertDate() {
            const input = document.getElementById('dateInput');
            const result = document.getElementById('dateResult');
            
            if (!input.value) {
                result.textContent = '请选择日期时间进行转换';
                result.className = 'result';
                return;
            }
            
            try {
                const date = new Date(input.value);
                if (isNaN(date.getTime())) {
                    result.textContent = '❌ 无效的日期时间';
                    result.className = 'result error';
                    return;
                }
                
                const timestamp = Math.floor(date.getTime() / 1000);
                result.textContent = '✅ ' + timestamp;
                result.className = 'result success';
            } catch (error) {
                result.textContent = '❌ 转换失败: ' + error.message;
                result.className = 'result error';
            }
        }

        function initializeApp() {
            try {
                updateCurrentTime();
                
                const now = new Date();
                const localISOTime = new Date(now.getTime() - now.getTimezoneOffset() * 60000)
                    .toISOString().slice(0, 16);
                document.getElementById('dateInput').value = localISOTime;
                convertDate();
                
                setInterval(updateCurrentTime, 1000);
                console.log('✅ 时间戳转换器稳定版本已成功加载');
            } catch (error) {
                console.error('❌ 应用初始化失败:', error);
            }
        }

        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializeApp);
        } else {
            initializeApp();
        }
    </script>
</body>
</html>
