{"version": 3, "file": "main-lY0MO8ZE.js", "sources": ["../../node_modules/react/cjs/react-jsx-dev-runtime.development.js", "../../node_modules/react/jsx-dev-runtime.js", "../../node_modules/react-dom/client.js", "../../src/SimpleApp.minimal.tsx", "../../src/main.minimal.tsx"], "sourcesContent": ["/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nif (process.env.NODE_ENV !== \"production\") {\n  (function() {\n'use strict';\n\nvar React = require('react');\n\n// ATTENTION\n// When adding new symbols to this file,\n// Please consider also adding to 'react-devtools-shared/src/backend/ReactSymbols'\n// The Symbol used to tag the ReactElement-like types.\nvar REACT_ELEMENT_TYPE = Symbol.for('react.element');\nvar REACT_PORTAL_TYPE = Symbol.for('react.portal');\nvar REACT_FRAGMENT_TYPE = Symbol.for('react.fragment');\nvar REACT_STRICT_MODE_TYPE = Symbol.for('react.strict_mode');\nvar REACT_PROFILER_TYPE = Symbol.for('react.profiler');\nvar REACT_PROVIDER_TYPE = Symbol.for('react.provider');\nvar REACT_CONTEXT_TYPE = Symbol.for('react.context');\nvar REACT_FORWARD_REF_TYPE = Symbol.for('react.forward_ref');\nvar REACT_SUSPENSE_TYPE = Symbol.for('react.suspense');\nvar REACT_SUSPENSE_LIST_TYPE = Symbol.for('react.suspense_list');\nvar REACT_MEMO_TYPE = Symbol.for('react.memo');\nvar REACT_LAZY_TYPE = Symbol.for('react.lazy');\nvar REACT_OFFSCREEN_TYPE = Symbol.for('react.offscreen');\nvar MAYBE_ITERATOR_SYMBOL = Symbol.iterator;\nvar FAUX_ITERATOR_SYMBOL = '@@iterator';\nfunction getIteratorFn(maybeIterable) {\n  if (maybeIterable === null || typeof maybeIterable !== 'object') {\n    return null;\n  }\n\n  var maybeIterator = MAYBE_ITERATOR_SYMBOL && maybeIterable[MAYBE_ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL];\n\n  if (typeof maybeIterator === 'function') {\n    return maybeIterator;\n  }\n\n  return null;\n}\n\nvar ReactSharedInternals = React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n\nfunction error(format) {\n  {\n    {\n      for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n        args[_key2 - 1] = arguments[_key2];\n      }\n\n      printWarning('error', format, args);\n    }\n  }\n}\n\nfunction printWarning(level, format, args) {\n  // When changing this logic, you might want to also\n  // update consoleWithStackDev.www.js as well.\n  {\n    var ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n    var stack = ReactDebugCurrentFrame.getStackAddendum();\n\n    if (stack !== '') {\n      format += '%s';\n      args = args.concat([stack]);\n    } // eslint-disable-next-line react-internal/safe-string-coercion\n\n\n    var argsWithFormat = args.map(function (item) {\n      return String(item);\n    }); // Careful: RN currently depends on this prefix\n\n    argsWithFormat.unshift('Warning: ' + format); // We intentionally don't use spread (or .apply) directly because it\n    // breaks IE9: https://github.com/facebook/react/issues/13610\n    // eslint-disable-next-line react-internal/no-production-logging\n\n    Function.prototype.apply.call(console[level], console, argsWithFormat);\n  }\n}\n\n// -----------------------------------------------------------------------------\n\nvar enableScopeAPI = false; // Experimental Create Event Handle API.\nvar enableCacheElement = false;\nvar enableTransitionTracing = false; // No known bugs, but needs performance testing\n\nvar enableLegacyHidden = false; // Enables unstable_avoidThisFallback feature in Fiber\n// stuff. Intended to enable React core members to more easily debug scheduling\n// issues in DEV builds.\n\nvar enableDebugTracing = false; // Track which Fiber(s) schedule render work.\n\nvar REACT_MODULE_REFERENCE;\n\n{\n  REACT_MODULE_REFERENCE = Symbol.for('react.module.reference');\n}\n\nfunction isValidElementType(type) {\n  if (typeof type === 'string' || typeof type === 'function') {\n    return true;\n  } // Note: typeof might be other than 'symbol' or 'number' (e.g. if it's a polyfill).\n\n\n  if (type === REACT_FRAGMENT_TYPE || type === REACT_PROFILER_TYPE || enableDebugTracing  || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || enableLegacyHidden  || type === REACT_OFFSCREEN_TYPE || enableScopeAPI  || enableCacheElement  || enableTransitionTracing ) {\n    return true;\n  }\n\n  if (typeof type === 'object' && type !== null) {\n    if (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_FORWARD_REF_TYPE || // This needs to include all possible module reference object\n    // types supported by any Flight configuration anywhere since\n    // we don't know which Flight build this will end up being used\n    // with.\n    type.$$typeof === REACT_MODULE_REFERENCE || type.getModuleId !== undefined) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nfunction getWrappedName(outerType, innerType, wrapperName) {\n  var displayName = outerType.displayName;\n\n  if (displayName) {\n    return displayName;\n  }\n\n  var functionName = innerType.displayName || innerType.name || '';\n  return functionName !== '' ? wrapperName + \"(\" + functionName + \")\" : wrapperName;\n} // Keep in sync with react-reconciler/getComponentNameFromFiber\n\n\nfunction getContextName(type) {\n  return type.displayName || 'Context';\n} // Note that the reconciler package should generally prefer to use getComponentNameFromFiber() instead.\n\n\nfunction getComponentNameFromType(type) {\n  if (type == null) {\n    // Host root, text node or just invalid type.\n    return null;\n  }\n\n  {\n    if (typeof type.tag === 'number') {\n      error('Received an unexpected object in getComponentNameFromType(). ' + 'This is likely a bug in React. Please file an issue.');\n    }\n  }\n\n  if (typeof type === 'function') {\n    return type.displayName || type.name || null;\n  }\n\n  if (typeof type === 'string') {\n    return type;\n  }\n\n  switch (type) {\n    case REACT_FRAGMENT_TYPE:\n      return 'Fragment';\n\n    case REACT_PORTAL_TYPE:\n      return 'Portal';\n\n    case REACT_PROFILER_TYPE:\n      return 'Profiler';\n\n    case REACT_STRICT_MODE_TYPE:\n      return 'StrictMode';\n\n    case REACT_SUSPENSE_TYPE:\n      return 'Suspense';\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return 'SuspenseList';\n\n  }\n\n  if (typeof type === 'object') {\n    switch (type.$$typeof) {\n      case REACT_CONTEXT_TYPE:\n        var context = type;\n        return getContextName(context) + '.Consumer';\n\n      case REACT_PROVIDER_TYPE:\n        var provider = type;\n        return getContextName(provider._context) + '.Provider';\n\n      case REACT_FORWARD_REF_TYPE:\n        return getWrappedName(type, type.render, 'ForwardRef');\n\n      case REACT_MEMO_TYPE:\n        var outerName = type.displayName || null;\n\n        if (outerName !== null) {\n          return outerName;\n        }\n\n        return getComponentNameFromType(type.type) || 'Memo';\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            return getComponentNameFromType(init(payload));\n          } catch (x) {\n            return null;\n          }\n        }\n\n      // eslint-disable-next-line no-fallthrough\n    }\n  }\n\n  return null;\n}\n\nvar assign = Object.assign;\n\n// Helpers to patch console.logs to avoid logging during side-effect free\n// replaying on render function. This currently only patches the object\n// lazily which won't cover if the log function was extracted eagerly.\n// We could also eagerly patch the method.\nvar disabledDepth = 0;\nvar prevLog;\nvar prevInfo;\nvar prevWarn;\nvar prevError;\nvar prevGroup;\nvar prevGroupCollapsed;\nvar prevGroupEnd;\n\nfunction disabledLog() {}\n\ndisabledLog.__reactDisabledLog = true;\nfunction disableLogs() {\n  {\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      prevLog = console.log;\n      prevInfo = console.info;\n      prevWarn = console.warn;\n      prevError = console.error;\n      prevGroup = console.group;\n      prevGroupCollapsed = console.groupCollapsed;\n      prevGroupEnd = console.groupEnd; // https://github.com/facebook/react/issues/19099\n\n      var props = {\n        configurable: true,\n        enumerable: true,\n        value: disabledLog,\n        writable: true\n      }; // $FlowFixMe Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        info: props,\n        log: props,\n        warn: props,\n        error: props,\n        group: props,\n        groupCollapsed: props,\n        groupEnd: props\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    disabledDepth++;\n  }\n}\nfunction reenableLogs() {\n  {\n    disabledDepth--;\n\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      var props = {\n        configurable: true,\n        enumerable: true,\n        writable: true\n      }; // $FlowFixMe Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        log: assign({}, props, {\n          value: prevLog\n        }),\n        info: assign({}, props, {\n          value: prevInfo\n        }),\n        warn: assign({}, props, {\n          value: prevWarn\n        }),\n        error: assign({}, props, {\n          value: prevError\n        }),\n        group: assign({}, props, {\n          value: prevGroup\n        }),\n        groupCollapsed: assign({}, props, {\n          value: prevGroupCollapsed\n        }),\n        groupEnd: assign({}, props, {\n          value: prevGroupEnd\n        })\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    if (disabledDepth < 0) {\n      error('disabledDepth fell below zero. ' + 'This is a bug in React. Please file an issue.');\n    }\n  }\n}\n\nvar ReactCurrentDispatcher = ReactSharedInternals.ReactCurrentDispatcher;\nvar prefix;\nfunction describeBuiltInComponentFrame(name, source, ownerFn) {\n  {\n    if (prefix === undefined) {\n      // Extract the VM specific prefix used by each line.\n      try {\n        throw Error();\n      } catch (x) {\n        var match = x.stack.trim().match(/\\n( *(at )?)/);\n        prefix = match && match[1] || '';\n      }\n    } // We use the prefix to ensure our stacks line up with native stack frames.\n\n\n    return '\\n' + prefix + name;\n  }\n}\nvar reentry = false;\nvar componentFrameCache;\n\n{\n  var PossiblyWeakMap = typeof WeakMap === 'function' ? WeakMap : Map;\n  componentFrameCache = new PossiblyWeakMap();\n}\n\nfunction describeNativeComponentFrame(fn, construct) {\n  // If something asked for a stack inside a fake render, it should get ignored.\n  if ( !fn || reentry) {\n    return '';\n  }\n\n  {\n    var frame = componentFrameCache.get(fn);\n\n    if (frame !== undefined) {\n      return frame;\n    }\n  }\n\n  var control;\n  reentry = true;\n  var previousPrepareStackTrace = Error.prepareStackTrace; // $FlowFixMe It does accept undefined.\n\n  Error.prepareStackTrace = undefined;\n  var previousDispatcher;\n\n  {\n    previousDispatcher = ReactCurrentDispatcher.current; // Set the dispatcher in DEV because this might be call in the render function\n    // for warnings.\n\n    ReactCurrentDispatcher.current = null;\n    disableLogs();\n  }\n\n  try {\n    // This should throw.\n    if (construct) {\n      // Something should be setting the props in the constructor.\n      var Fake = function () {\n        throw Error();\n      }; // $FlowFixMe\n\n\n      Object.defineProperty(Fake.prototype, 'props', {\n        set: function () {\n          // We use a throwing setter instead of frozen or non-writable props\n          // because that won't throw in a non-strict mode function.\n          throw Error();\n        }\n      });\n\n      if (typeof Reflect === 'object' && Reflect.construct) {\n        // We construct a different control for this case to include any extra\n        // frames added by the construct call.\n        try {\n          Reflect.construct(Fake, []);\n        } catch (x) {\n          control = x;\n        }\n\n        Reflect.construct(fn, [], Fake);\n      } else {\n        try {\n          Fake.call();\n        } catch (x) {\n          control = x;\n        }\n\n        fn.call(Fake.prototype);\n      }\n    } else {\n      try {\n        throw Error();\n      } catch (x) {\n        control = x;\n      }\n\n      fn();\n    }\n  } catch (sample) {\n    // This is inlined manually because closure doesn't do it for us.\n    if (sample && control && typeof sample.stack === 'string') {\n      // This extracts the first frame from the sample that isn't also in the control.\n      // Skipping one frame that we assume is the frame that calls the two.\n      var sampleLines = sample.stack.split('\\n');\n      var controlLines = control.stack.split('\\n');\n      var s = sampleLines.length - 1;\n      var c = controlLines.length - 1;\n\n      while (s >= 1 && c >= 0 && sampleLines[s] !== controlLines[c]) {\n        // We expect at least one stack frame to be shared.\n        // Typically this will be the root most one. However, stack frames may be\n        // cut off due to maximum stack limits. In this case, one maybe cut off\n        // earlier than the other. We assume that the sample is longer or the same\n        // and there for cut off earlier. So we should find the root most frame in\n        // the sample somewhere in the control.\n        c--;\n      }\n\n      for (; s >= 1 && c >= 0; s--, c--) {\n        // Next we find the first one that isn't the same which should be the\n        // frame that called our sample function and the control.\n        if (sampleLines[s] !== controlLines[c]) {\n          // In V8, the first line is describing the message but other VMs don't.\n          // If we're about to return the first line, and the control is also on the same\n          // line, that's a pretty good indicator that our sample threw at same line as\n          // the control. I.e. before we entered the sample frame. So we ignore this result.\n          // This can happen if you passed a class to function component, or non-function.\n          if (s !== 1 || c !== 1) {\n            do {\n              s--;\n              c--; // We may still have similar intermediate frames from the construct call.\n              // The next one that isn't the same should be our match though.\n\n              if (c < 0 || sampleLines[s] !== controlLines[c]) {\n                // V8 adds a \"new\" prefix for native classes. Let's remove it to make it prettier.\n                var _frame = '\\n' + sampleLines[s].replace(' at new ', ' at '); // If our component frame is labeled \"<anonymous>\"\n                // but we have a user-provided \"displayName\"\n                // splice it in to make the stack more readable.\n\n\n                if (fn.displayName && _frame.includes('<anonymous>')) {\n                  _frame = _frame.replace('<anonymous>', fn.displayName);\n                }\n\n                {\n                  if (typeof fn === 'function') {\n                    componentFrameCache.set(fn, _frame);\n                  }\n                } // Return the line we found.\n\n\n                return _frame;\n              }\n            } while (s >= 1 && c >= 0);\n          }\n\n          break;\n        }\n      }\n    }\n  } finally {\n    reentry = false;\n\n    {\n      ReactCurrentDispatcher.current = previousDispatcher;\n      reenableLogs();\n    }\n\n    Error.prepareStackTrace = previousPrepareStackTrace;\n  } // Fallback to just using the name if we couldn't make it throw.\n\n\n  var name = fn ? fn.displayName || fn.name : '';\n  var syntheticFrame = name ? describeBuiltInComponentFrame(name) : '';\n\n  {\n    if (typeof fn === 'function') {\n      componentFrameCache.set(fn, syntheticFrame);\n    }\n  }\n\n  return syntheticFrame;\n}\nfunction describeFunctionComponentFrame(fn, source, ownerFn) {\n  {\n    return describeNativeComponentFrame(fn, false);\n  }\n}\n\nfunction shouldConstruct(Component) {\n  var prototype = Component.prototype;\n  return !!(prototype && prototype.isReactComponent);\n}\n\nfunction describeUnknownElementTypeFrameInDEV(type, source, ownerFn) {\n\n  if (type == null) {\n    return '';\n  }\n\n  if (typeof type === 'function') {\n    {\n      return describeNativeComponentFrame(type, shouldConstruct(type));\n    }\n  }\n\n  if (typeof type === 'string') {\n    return describeBuiltInComponentFrame(type);\n  }\n\n  switch (type) {\n    case REACT_SUSPENSE_TYPE:\n      return describeBuiltInComponentFrame('Suspense');\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return describeBuiltInComponentFrame('SuspenseList');\n  }\n\n  if (typeof type === 'object') {\n    switch (type.$$typeof) {\n      case REACT_FORWARD_REF_TYPE:\n        return describeFunctionComponentFrame(type.render);\n\n      case REACT_MEMO_TYPE:\n        // Memo may contain any component type so we recursively resolve it.\n        return describeUnknownElementTypeFrameInDEV(type.type, source, ownerFn);\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            // Lazy may contain any component type so we recursively resolve it.\n            return describeUnknownElementTypeFrameInDEV(init(payload), source, ownerFn);\n          } catch (x) {}\n        }\n    }\n  }\n\n  return '';\n}\n\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\n\nvar loggedTypeFailures = {};\nvar ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n\nfunction setCurrentlyValidatingElement(element) {\n  {\n    if (element) {\n      var owner = element._owner;\n      var stack = describeUnknownElementTypeFrameInDEV(element.type, element._source, owner ? owner.type : null);\n      ReactDebugCurrentFrame.setExtraStackFrame(stack);\n    } else {\n      ReactDebugCurrentFrame.setExtraStackFrame(null);\n    }\n  }\n}\n\nfunction checkPropTypes(typeSpecs, values, location, componentName, element) {\n  {\n    // $FlowFixMe This is okay but Flow doesn't know it.\n    var has = Function.call.bind(hasOwnProperty);\n\n    for (var typeSpecName in typeSpecs) {\n      if (has(typeSpecs, typeSpecName)) {\n        var error$1 = void 0; // Prop type validation may throw. In case they do, we don't want to\n        // fail the render phase where it didn't fail before. So we log it.\n        // After these have been cleaned up, we'll let them throw.\n\n        try {\n          // This is intentionally an invariant that gets caught. It's the same\n          // behavior as without this statement except with a better message.\n          if (typeof typeSpecs[typeSpecName] !== 'function') {\n            // eslint-disable-next-line react-internal/prod-error-codes\n            var err = Error((componentName || 'React class') + ': ' + location + ' type `' + typeSpecName + '` is invalid; ' + 'it must be a function, usually from the `prop-types` package, but received `' + typeof typeSpecs[typeSpecName] + '`.' + 'This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.');\n            err.name = 'Invariant Violation';\n            throw err;\n          }\n\n          error$1 = typeSpecs[typeSpecName](values, typeSpecName, componentName, location, null, 'SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED');\n        } catch (ex) {\n          error$1 = ex;\n        }\n\n        if (error$1 && !(error$1 instanceof Error)) {\n          setCurrentlyValidatingElement(element);\n\n          error('%s: type specification of %s' + ' `%s` is invalid; the type checker ' + 'function must return `null` or an `Error` but returned a %s. ' + 'You may have forgotten to pass an argument to the type checker ' + 'creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and ' + 'shape all require an argument).', componentName || 'React class', location, typeSpecName, typeof error$1);\n\n          setCurrentlyValidatingElement(null);\n        }\n\n        if (error$1 instanceof Error && !(error$1.message in loggedTypeFailures)) {\n          // Only monitor this failure once because there tends to be a lot of the\n          // same error.\n          loggedTypeFailures[error$1.message] = true;\n          setCurrentlyValidatingElement(element);\n\n          error('Failed %s type: %s', location, error$1.message);\n\n          setCurrentlyValidatingElement(null);\n        }\n      }\n    }\n  }\n}\n\nvar isArrayImpl = Array.isArray; // eslint-disable-next-line no-redeclare\n\nfunction isArray(a) {\n  return isArrayImpl(a);\n}\n\n/*\n * The `'' + value` pattern (used in in perf-sensitive code) throws for Symbol\n * and Temporal.* types. See https://github.com/facebook/react/pull/22064.\n *\n * The functions in this module will throw an easier-to-understand,\n * easier-to-debug exception with a clear errors message message explaining the\n * problem. (Instead of a confusing exception thrown inside the implementation\n * of the `value` object).\n */\n// $FlowFixMe only called in DEV, so void return is not possible.\nfunction typeName(value) {\n  {\n    // toStringTag is needed for namespaced types like Temporal.Instant\n    var hasToStringTag = typeof Symbol === 'function' && Symbol.toStringTag;\n    var type = hasToStringTag && value[Symbol.toStringTag] || value.constructor.name || 'Object';\n    return type;\n  }\n} // $FlowFixMe only called in DEV, so void return is not possible.\n\n\nfunction willCoercionThrow(value) {\n  {\n    try {\n      testStringCoercion(value);\n      return false;\n    } catch (e) {\n      return true;\n    }\n  }\n}\n\nfunction testStringCoercion(value) {\n  // If you ended up here by following an exception call stack, here's what's\n  // happened: you supplied an object or symbol value to React (as a prop, key,\n  // DOM attribute, CSS property, string ref, etc.) and when React tried to\n  // coerce it to a string using `'' + value`, an exception was thrown.\n  //\n  // The most common types that will cause this exception are `Symbol` instances\n  // and Temporal objects like `Temporal.Instant`. But any object that has a\n  // `valueOf` or `[Symbol.toPrimitive]` method that throws will also cause this\n  // exception. (Library authors do this to prevent users from using built-in\n  // numeric operators like `+` or comparison operators like `>=` because custom\n  // methods are needed to perform accurate arithmetic or comparison.)\n  //\n  // To fix the problem, coerce this object or symbol value to a string before\n  // passing it to React. The most reliable way is usually `String(value)`.\n  //\n  // To find which value is throwing, check the browser or debugger console.\n  // Before this exception was thrown, there should be `console.error` output\n  // that shows the type (Symbol, Temporal.PlainDate, etc.) that caused the\n  // problem and how that type was used: key, atrribute, input value prop, etc.\n  // In most cases, this console output also shows the component and its\n  // ancestor components where the exception happened.\n  //\n  // eslint-disable-next-line react-internal/safe-string-coercion\n  return '' + value;\n}\nfunction checkKeyStringCoercion(value) {\n  {\n    if (willCoercionThrow(value)) {\n      error('The provided key is an unsupported type %s.' + ' This value must be coerced to a string before before using it here.', typeName(value));\n\n      return testStringCoercion(value); // throw (to help callers find troubleshooting comments)\n    }\n  }\n}\n\nvar ReactCurrentOwner = ReactSharedInternals.ReactCurrentOwner;\nvar RESERVED_PROPS = {\n  key: true,\n  ref: true,\n  __self: true,\n  __source: true\n};\nvar specialPropKeyWarningShown;\nvar specialPropRefWarningShown;\nvar didWarnAboutStringRefs;\n\n{\n  didWarnAboutStringRefs = {};\n}\n\nfunction hasValidRef(config) {\n  {\n    if (hasOwnProperty.call(config, 'ref')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'ref').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.ref !== undefined;\n}\n\nfunction hasValidKey(config) {\n  {\n    if (hasOwnProperty.call(config, 'key')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'key').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.key !== undefined;\n}\n\nfunction warnIfStringRefCannotBeAutoConverted(config, self) {\n  {\n    if (typeof config.ref === 'string' && ReactCurrentOwner.current && self && ReactCurrentOwner.current.stateNode !== self) {\n      var componentName = getComponentNameFromType(ReactCurrentOwner.current.type);\n\n      if (!didWarnAboutStringRefs[componentName]) {\n        error('Component \"%s\" contains the string ref \"%s\". ' + 'Support for string refs will be removed in a future major release. ' + 'This case cannot be automatically converted to an arrow function. ' + 'We ask you to manually fix this case by using useRef() or createRef() instead. ' + 'Learn more about using refs safely here: ' + 'https://reactjs.org/link/strict-mode-string-ref', getComponentNameFromType(ReactCurrentOwner.current.type), config.ref);\n\n        didWarnAboutStringRefs[componentName] = true;\n      }\n    }\n  }\n}\n\nfunction defineKeyPropWarningGetter(props, displayName) {\n  {\n    var warnAboutAccessingKey = function () {\n      if (!specialPropKeyWarningShown) {\n        specialPropKeyWarningShown = true;\n\n        error('%s: `key` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n      }\n    };\n\n    warnAboutAccessingKey.isReactWarning = true;\n    Object.defineProperty(props, 'key', {\n      get: warnAboutAccessingKey,\n      configurable: true\n    });\n  }\n}\n\nfunction defineRefPropWarningGetter(props, displayName) {\n  {\n    var warnAboutAccessingRef = function () {\n      if (!specialPropRefWarningShown) {\n        specialPropRefWarningShown = true;\n\n        error('%s: `ref` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n      }\n    };\n\n    warnAboutAccessingRef.isReactWarning = true;\n    Object.defineProperty(props, 'ref', {\n      get: warnAboutAccessingRef,\n      configurable: true\n    });\n  }\n}\n/**\n * Factory method to create a new React element. This no longer adheres to\n * the class pattern, so do not use new to call it. Also, instanceof check\n * will not work. Instead test $$typeof field against Symbol.for('react.element') to check\n * if something is a React Element.\n *\n * @param {*} type\n * @param {*} props\n * @param {*} key\n * @param {string|object} ref\n * @param {*} owner\n * @param {*} self A *temporary* helper to detect places where `this` is\n * different from the `owner` when React.createElement is called, so that we\n * can warn. We want to get rid of owner and replace string `ref`s with arrow\n * functions, and as long as `this` and owner are the same, there will be no\n * change in behavior.\n * @param {*} source An annotation object (added by a transpiler or otherwise)\n * indicating filename, line number, and/or other information.\n * @internal\n */\n\n\nvar ReactElement = function (type, key, ref, self, source, owner, props) {\n  var element = {\n    // This tag allows us to uniquely identify this as a React Element\n    $$typeof: REACT_ELEMENT_TYPE,\n    // Built-in properties that belong on the element\n    type: type,\n    key: key,\n    ref: ref,\n    props: props,\n    // Record the component responsible for creating this element.\n    _owner: owner\n  };\n\n  {\n    // The validation flag is currently mutative. We put it on\n    // an external backing store so that we can freeze the whole object.\n    // This can be replaced with a WeakMap once they are implemented in\n    // commonly used development environments.\n    element._store = {}; // To make comparing ReactElements easier for testing purposes, we make\n    // the validation flag non-enumerable (where possible, which should\n    // include every environment we run tests in), so the test framework\n    // ignores it.\n\n    Object.defineProperty(element._store, 'validated', {\n      configurable: false,\n      enumerable: false,\n      writable: true,\n      value: false\n    }); // self and source are DEV only properties.\n\n    Object.defineProperty(element, '_self', {\n      configurable: false,\n      enumerable: false,\n      writable: false,\n      value: self\n    }); // Two elements created in two different places should be considered\n    // equal for testing purposes and therefore we hide it from enumeration.\n\n    Object.defineProperty(element, '_source', {\n      configurable: false,\n      enumerable: false,\n      writable: false,\n      value: source\n    });\n\n    if (Object.freeze) {\n      Object.freeze(element.props);\n      Object.freeze(element);\n    }\n  }\n\n  return element;\n};\n/**\n * https://github.com/reactjs/rfcs/pull/107\n * @param {*} type\n * @param {object} props\n * @param {string} key\n */\n\nfunction jsxDEV(type, config, maybeKey, source, self) {\n  {\n    var propName; // Reserved names are extracted\n\n    var props = {};\n    var key = null;\n    var ref = null; // Currently, key can be spread in as a prop. This causes a potential\n    // issue if key is also explicitly declared (ie. <div {...props} key=\"Hi\" />\n    // or <div key=\"Hi\" {...props} /> ). We want to deprecate key spread,\n    // but as an intermediary step, we will use jsxDEV for everything except\n    // <div {...props} key=\"Hi\" />, because we aren't currently able to tell if\n    // key is explicitly declared to be undefined or not.\n\n    if (maybeKey !== undefined) {\n      {\n        checkKeyStringCoercion(maybeKey);\n      }\n\n      key = '' + maybeKey;\n    }\n\n    if (hasValidKey(config)) {\n      {\n        checkKeyStringCoercion(config.key);\n      }\n\n      key = '' + config.key;\n    }\n\n    if (hasValidRef(config)) {\n      ref = config.ref;\n      warnIfStringRefCannotBeAutoConverted(config, self);\n    } // Remaining properties are added to a new props object\n\n\n    for (propName in config) {\n      if (hasOwnProperty.call(config, propName) && !RESERVED_PROPS.hasOwnProperty(propName)) {\n        props[propName] = config[propName];\n      }\n    } // Resolve default props\n\n\n    if (type && type.defaultProps) {\n      var defaultProps = type.defaultProps;\n\n      for (propName in defaultProps) {\n        if (props[propName] === undefined) {\n          props[propName] = defaultProps[propName];\n        }\n      }\n    }\n\n    if (key || ref) {\n      var displayName = typeof type === 'function' ? type.displayName || type.name || 'Unknown' : type;\n\n      if (key) {\n        defineKeyPropWarningGetter(props, displayName);\n      }\n\n      if (ref) {\n        defineRefPropWarningGetter(props, displayName);\n      }\n    }\n\n    return ReactElement(type, key, ref, self, source, ReactCurrentOwner.current, props);\n  }\n}\n\nvar ReactCurrentOwner$1 = ReactSharedInternals.ReactCurrentOwner;\nvar ReactDebugCurrentFrame$1 = ReactSharedInternals.ReactDebugCurrentFrame;\n\nfunction setCurrentlyValidatingElement$1(element) {\n  {\n    if (element) {\n      var owner = element._owner;\n      var stack = describeUnknownElementTypeFrameInDEV(element.type, element._source, owner ? owner.type : null);\n      ReactDebugCurrentFrame$1.setExtraStackFrame(stack);\n    } else {\n      ReactDebugCurrentFrame$1.setExtraStackFrame(null);\n    }\n  }\n}\n\nvar propTypesMisspellWarningShown;\n\n{\n  propTypesMisspellWarningShown = false;\n}\n/**\n * Verifies the object is a ReactElement.\n * See https://reactjs.org/docs/react-api.html#isvalidelement\n * @param {?object} object\n * @return {boolean} True if `object` is a ReactElement.\n * @final\n */\n\n\nfunction isValidElement(object) {\n  {\n    return typeof object === 'object' && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;\n  }\n}\n\nfunction getDeclarationErrorAddendum() {\n  {\n    if (ReactCurrentOwner$1.current) {\n      var name = getComponentNameFromType(ReactCurrentOwner$1.current.type);\n\n      if (name) {\n        return '\\n\\nCheck the render method of `' + name + '`.';\n      }\n    }\n\n    return '';\n  }\n}\n\nfunction getSourceInfoErrorAddendum(source) {\n  {\n    if (source !== undefined) {\n      var fileName = source.fileName.replace(/^.*[\\\\\\/]/, '');\n      var lineNumber = source.lineNumber;\n      return '\\n\\nCheck your code at ' + fileName + ':' + lineNumber + '.';\n    }\n\n    return '';\n  }\n}\n/**\n * Warn if there's no key explicitly set on dynamic arrays of children or\n * object keys are not valid. This allows us to keep track of children between\n * updates.\n */\n\n\nvar ownerHasKeyUseWarning = {};\n\nfunction getCurrentComponentErrorInfo(parentType) {\n  {\n    var info = getDeclarationErrorAddendum();\n\n    if (!info) {\n      var parentName = typeof parentType === 'string' ? parentType : parentType.displayName || parentType.name;\n\n      if (parentName) {\n        info = \"\\n\\nCheck the top-level render call using <\" + parentName + \">.\";\n      }\n    }\n\n    return info;\n  }\n}\n/**\n * Warn if the element doesn't have an explicit key assigned to it.\n * This element is in an array. The array could grow and shrink or be\n * reordered. All children that haven't already been validated are required to\n * have a \"key\" property assigned to it. Error statuses are cached so a warning\n * will only be shown once.\n *\n * @internal\n * @param {ReactElement} element Element that requires a key.\n * @param {*} parentType element's parent's type.\n */\n\n\nfunction validateExplicitKey(element, parentType) {\n  {\n    if (!element._store || element._store.validated || element.key != null) {\n      return;\n    }\n\n    element._store.validated = true;\n    var currentComponentErrorInfo = getCurrentComponentErrorInfo(parentType);\n\n    if (ownerHasKeyUseWarning[currentComponentErrorInfo]) {\n      return;\n    }\n\n    ownerHasKeyUseWarning[currentComponentErrorInfo] = true; // Usually the current owner is the offender, but if it accepts children as a\n    // property, it may be the creator of the child that's responsible for\n    // assigning it a key.\n\n    var childOwner = '';\n\n    if (element && element._owner && element._owner !== ReactCurrentOwner$1.current) {\n      // Give the component that originally created this child.\n      childOwner = \" It was passed a child from \" + getComponentNameFromType(element._owner.type) + \".\";\n    }\n\n    setCurrentlyValidatingElement$1(element);\n\n    error('Each child in a list should have a unique \"key\" prop.' + '%s%s See https://reactjs.org/link/warning-keys for more information.', currentComponentErrorInfo, childOwner);\n\n    setCurrentlyValidatingElement$1(null);\n  }\n}\n/**\n * Ensure that every element either is passed in a static location, in an\n * array with an explicit keys property defined, or in an object literal\n * with valid key property.\n *\n * @internal\n * @param {ReactNode} node Statically passed child of any type.\n * @param {*} parentType node's parent's type.\n */\n\n\nfunction validateChildKeys(node, parentType) {\n  {\n    if (typeof node !== 'object') {\n      return;\n    }\n\n    if (isArray(node)) {\n      for (var i = 0; i < node.length; i++) {\n        var child = node[i];\n\n        if (isValidElement(child)) {\n          validateExplicitKey(child, parentType);\n        }\n      }\n    } else if (isValidElement(node)) {\n      // This element was passed in a valid location.\n      if (node._store) {\n        node._store.validated = true;\n      }\n    } else if (node) {\n      var iteratorFn = getIteratorFn(node);\n\n      if (typeof iteratorFn === 'function') {\n        // Entry iterators used to provide implicit keys,\n        // but now we print a separate warning for them later.\n        if (iteratorFn !== node.entries) {\n          var iterator = iteratorFn.call(node);\n          var step;\n\n          while (!(step = iterator.next()).done) {\n            if (isValidElement(step.value)) {\n              validateExplicitKey(step.value, parentType);\n            }\n          }\n        }\n      }\n    }\n  }\n}\n/**\n * Given an element, validate that its props follow the propTypes definition,\n * provided by the type.\n *\n * @param {ReactElement} element\n */\n\n\nfunction validatePropTypes(element) {\n  {\n    var type = element.type;\n\n    if (type === null || type === undefined || typeof type === 'string') {\n      return;\n    }\n\n    var propTypes;\n\n    if (typeof type === 'function') {\n      propTypes = type.propTypes;\n    } else if (typeof type === 'object' && (type.$$typeof === REACT_FORWARD_REF_TYPE || // Note: Memo only checks outer props here.\n    // Inner props are checked in the reconciler.\n    type.$$typeof === REACT_MEMO_TYPE)) {\n      propTypes = type.propTypes;\n    } else {\n      return;\n    }\n\n    if (propTypes) {\n      // Intentionally inside to avoid triggering lazy initializers:\n      var name = getComponentNameFromType(type);\n      checkPropTypes(propTypes, element.props, 'prop', name, element);\n    } else if (type.PropTypes !== undefined && !propTypesMisspellWarningShown) {\n      propTypesMisspellWarningShown = true; // Intentionally inside to avoid triggering lazy initializers:\n\n      var _name = getComponentNameFromType(type);\n\n      error('Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?', _name || 'Unknown');\n    }\n\n    if (typeof type.getDefaultProps === 'function' && !type.getDefaultProps.isReactClassApproved) {\n      error('getDefaultProps is only used on classic React.createClass ' + 'definitions. Use a static property named `defaultProps` instead.');\n    }\n  }\n}\n/**\n * Given a fragment, validate that it can only be provided with fragment props\n * @param {ReactElement} fragment\n */\n\n\nfunction validateFragmentProps(fragment) {\n  {\n    var keys = Object.keys(fragment.props);\n\n    for (var i = 0; i < keys.length; i++) {\n      var key = keys[i];\n\n      if (key !== 'children' && key !== 'key') {\n        setCurrentlyValidatingElement$1(fragment);\n\n        error('Invalid prop `%s` supplied to `React.Fragment`. ' + 'React.Fragment can only have `key` and `children` props.', key);\n\n        setCurrentlyValidatingElement$1(null);\n        break;\n      }\n    }\n\n    if (fragment.ref !== null) {\n      setCurrentlyValidatingElement$1(fragment);\n\n      error('Invalid attribute `ref` supplied to `React.Fragment`.');\n\n      setCurrentlyValidatingElement$1(null);\n    }\n  }\n}\n\nvar didWarnAboutKeySpread = {};\nfunction jsxWithValidation(type, props, key, isStaticChildren, source, self) {\n  {\n    var validType = isValidElementType(type); // We warn in this case but don't throw. We expect the element creation to\n    // succeed and there will likely be errors in render.\n\n    if (!validType) {\n      var info = '';\n\n      if (type === undefined || typeof type === 'object' && type !== null && Object.keys(type).length === 0) {\n        info += ' You likely forgot to export your component from the file ' + \"it's defined in, or you might have mixed up default and named imports.\";\n      }\n\n      var sourceInfo = getSourceInfoErrorAddendum(source);\n\n      if (sourceInfo) {\n        info += sourceInfo;\n      } else {\n        info += getDeclarationErrorAddendum();\n      }\n\n      var typeString;\n\n      if (type === null) {\n        typeString = 'null';\n      } else if (isArray(type)) {\n        typeString = 'array';\n      } else if (type !== undefined && type.$$typeof === REACT_ELEMENT_TYPE) {\n        typeString = \"<\" + (getComponentNameFromType(type.type) || 'Unknown') + \" />\";\n        info = ' Did you accidentally export a JSX literal instead of a component?';\n      } else {\n        typeString = typeof type;\n      }\n\n      error('React.jsx: type is invalid -- expected a string (for ' + 'built-in components) or a class/function (for composite ' + 'components) but got: %s.%s', typeString, info);\n    }\n\n    var element = jsxDEV(type, props, key, source, self); // The result can be nullish if a mock or a custom function is used.\n    // TODO: Drop this when these are no longer allowed as the type argument.\n\n    if (element == null) {\n      return element;\n    } // Skip key warning if the type isn't valid since our key validation logic\n    // doesn't expect a non-string/function type and can throw confusing errors.\n    // We don't want exception behavior to differ between dev and prod.\n    // (Rendering will throw with a helpful message and as soon as the type is\n    // fixed, the key warnings will appear.)\n\n\n    if (validType) {\n      var children = props.children;\n\n      if (children !== undefined) {\n        if (isStaticChildren) {\n          if (isArray(children)) {\n            for (var i = 0; i < children.length; i++) {\n              validateChildKeys(children[i], type);\n            }\n\n            if (Object.freeze) {\n              Object.freeze(children);\n            }\n          } else {\n            error('React.jsx: Static children should always be an array. ' + 'You are likely explicitly calling React.jsxs or React.jsxDEV. ' + 'Use the Babel transform instead.');\n          }\n        } else {\n          validateChildKeys(children, type);\n        }\n      }\n    }\n\n    {\n      if (hasOwnProperty.call(props, 'key')) {\n        var componentName = getComponentNameFromType(type);\n        var keys = Object.keys(props).filter(function (k) {\n          return k !== 'key';\n        });\n        var beforeExample = keys.length > 0 ? '{key: someKey, ' + keys.join(': ..., ') + ': ...}' : '{key: someKey}';\n\n        if (!didWarnAboutKeySpread[componentName + beforeExample]) {\n          var afterExample = keys.length > 0 ? '{' + keys.join(': ..., ') + ': ...}' : '{}';\n\n          error('A props object containing a \"key\" prop is being spread into JSX:\\n' + '  let props = %s;\\n' + '  <%s {...props} />\\n' + 'React keys must be passed directly to JSX without using spread:\\n' + '  let props = %s;\\n' + '  <%s key={someKey} {...props} />', beforeExample, componentName, afterExample, componentName);\n\n          didWarnAboutKeySpread[componentName + beforeExample] = true;\n        }\n      }\n    }\n\n    if (type === REACT_FRAGMENT_TYPE) {\n      validateFragmentProps(element);\n    } else {\n      validatePropTypes(element);\n    }\n\n    return element;\n  }\n} // These two functions exist to still get child warnings in dev\n\nvar jsxDEV$1 =  jsxWithValidation ;\n\nexports.Fragment = REACT_FRAGMENT_TYPE;\nexports.jsxDEV = jsxDEV$1;\n  })();\n}\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-dev-runtime.production.min.js');\n} else {\n  module.exports = require('./cjs/react-jsx-dev-runtime.development.js');\n}\n", "'use strict';\n\nvar m = require('react-dom');\nif (process.env.NODE_ENV === 'production') {\n  exports.createRoot = m.createRoot;\n  exports.hydrateRoot = m.hydrateRoot;\n} else {\n  var i = m.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n  exports.createRoot = function(c, o) {\n    i.usingClientEntryPoint = true;\n    try {\n      return m.createRoot(c, o);\n    } finally {\n      i.usingClientEntryPoint = false;\n    }\n  };\n  exports.hydrateRoot = function(c, h, o) {\n    i.usingClientEntryPoint = true;\n    try {\n      return m.hydrateRoot(c, h, o);\n    } finally {\n      i.usingClientEntryPoint = false;\n    }\n  };\n}\n", "import React, { useState, useEffect } from 'react';\nimport './index.css';\n\n// 简化的时间戳转换器组件，不使用任何外部 UI 库\nfunction SimpleTimestampConverter() {\n  const [timestamp, setTimestamp] = useState(Math.floor(Date.now() / 1000));\n  const [dateInput, setDateInput] = useState('');\n  const [isDark, setIsDark] = useState(false);\n\n  useEffect(() => {\n    // 检测系统主题偏好\n    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n    setIsDark(prefersDark);\n    \n    // 设置当前时间\n    const now = new Date();\n    setDateInput(now.toISOString().slice(0, 16));\n  }, []);\n\n  useEffect(() => {\n    // 应用主题\n    if (isDark) {\n      document.documentElement.classList.add('dark');\n    } else {\n      document.documentElement.classList.remove('dark');\n    }\n  }, [isDark]);\n\n  const handleTimestampChange = (value: string) => {\n    const ts = parseInt(value);\n    if (!isNaN(ts) && ts > 0) {\n      setTimestamp(ts);\n      const date = new Date(ts * 1000);\n      setDateInput(date.toISOString().slice(0, 16));\n    }\n  };\n\n  const handleDateChange = (value: string) => {\n    setDateInput(value);\n    const date = new Date(value);\n    if (!isNaN(date.getTime())) {\n      setTimestamp(Math.floor(date.getTime() / 1000));\n    }\n  };\n\n  const updateToNow = () => {\n    const now = Math.floor(Date.now() / 1000);\n    setTimestamp(now);\n    setDateInput(new Date().toISOString().slice(0, 16));\n  };\n\n  const formatDate = (ts: number) => {\n    try {\n      return new Date(ts * 1000).toLocaleString('zh-CN', {\n        timeZone: 'Asia/Shanghai',\n        year: 'numeric',\n        month: '2-digit',\n        day: '2-digit',\n        hour: '2-digit',\n        minute: '2-digit',\n        second: '2-digit'\n      });\n    } catch (error) {\n      return new Date(ts * 1000).toLocaleString();\n    }\n  };\n\n  return (\n    <div className={`min-h-screen p-4 transition-colors duration-300 ${\n      isDark ? 'bg-gray-900 text-white' : 'bg-gradient-to-br from-blue-500 to-purple-600 text-white'\n    }`}>\n      {/* Header */}\n      <header className=\"text-center mb-8\">\n        <h1 className=\"text-4xl font-bold mb-2\">时间戳转换器</h1>\n        <p className=\"text-lg opacity-90\">Unix时间戳与日期时间双向转换工具</p>\n        <button\n          onClick={() => setIsDark(!isDark)}\n          className=\"mt-4 px-4 py-2 rounded-lg bg-white bg-opacity-20 hover:bg-opacity-30 transition-all duration-200\"\n        >\n          {isDark ? '🌞 浅色模式' : '🌙 深色模式'}\n        </button>\n      </header>\n\n      {/* Main Content */}\n      <div className=\"max-w-4xl mx-auto\">\n        {/* Current Time Card */}\n        <div className=\"bg-white bg-opacity-10 backdrop-blur-md rounded-xl p-6 mb-6 border border-white border-opacity-20\">\n          <h2 className=\"text-xl font-semibold mb-4\">当前时间</h2>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div>\n              <p className=\"text-sm opacity-75 mb-1\">时间戳</p>\n              <p className=\"text-2xl font-mono\">{Math.floor(Date.now() / 1000)}</p>\n            </div>\n            <div>\n              <p className=\"text-sm opacity-75 mb-1\">日期时间</p>\n              <p className=\"text-lg\">{formatDate(Math.floor(Date.now() / 1000))}</p>\n            </div>\n          </div>\n          <button\n            onClick={updateToNow}\n            className=\"mt-4 px-6 py-2 bg-blue-500 hover:bg-blue-600 rounded-lg transition-colors duration-200\"\n          >\n            更新到当前时间\n          </button>\n        </div>\n\n        {/* Converter Cards */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n          {/* Timestamp to Date */}\n          <div className=\"bg-white bg-opacity-10 backdrop-blur-md rounded-xl p-6 border border-white border-opacity-20\">\n            <h3 className=\"text-lg font-semibold mb-4\">时间戳转日期</h3>\n            <input\n              type=\"number\"\n              value={timestamp}\n              onChange={(e) => handleTimestampChange(e.target.value)}\n              className=\"w-full p-3 rounded-lg bg-white bg-opacity-20 border border-white border-opacity-30 placeholder-white placeholder-opacity-70 text-white\"\n              placeholder=\"输入时间戳\"\n            />\n            <div className=\"mt-4 p-3 bg-black bg-opacity-20 rounded-lg\">\n              <p className=\"text-sm opacity-75 mb-1\">转换结果</p>\n              <p className=\"font-mono\">{formatDate(timestamp)}</p>\n            </div>\n          </div>\n\n          {/* Date to Timestamp */}\n          <div className=\"bg-white bg-opacity-10 backdrop-blur-md rounded-xl p-6 border border-white border-opacity-20\">\n            <h3 className=\"text-lg font-semibold mb-4\">日期转时间戳</h3>\n            <input\n              type=\"datetime-local\"\n              value={dateInput}\n              onChange={(e) => handleDateChange(e.target.value)}\n              className=\"w-full p-3 rounded-lg bg-white bg-opacity-20 border border-white border-opacity-30 text-white\"\n            />\n            <div className=\"mt-4 p-3 bg-black bg-opacity-20 rounded-lg\">\n              <p className=\"text-sm opacity-75 mb-1\">转换结果</p>\n              <p className=\"font-mono text-xl\">{timestamp}</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Footer */}\n      <footer className=\"text-center mt-12 opacity-75\">\n        <p>© 2024 时间戳转换器 - 免费在线工具</p>\n        <p className=\"text-sm mt-2\">简化版本 - 无外部依赖</p>\n      </footer>\n    </div>\n  );\n}\n\nfunction MinimalApp() {\n  return (\n    <React.StrictMode>\n      <SimpleTimestampConverter />\n    </React.StrictMode>\n  );\n}\n\nexport default MinimalApp;\n", "import React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport MinimalApp from './SimpleApp.minimal';\nimport './index.css';\n\n// 简化的入口文件，移除所有可能有问题的依赖\nconsole.log('✅ 简化版本启动中...');\n\n// 基本的错误处理\nwindow.addEventListener('error', (event) => {\n  console.error('全局错误:', event.error);\n});\n\nwindow.addEventListener('unhandledrejection', (event) => {\n  console.error('未处理的 Promise 拒绝:', event.reason);\n});\n\n// 渲染应用\nconst root = ReactDOM.createRoot(document.getElementById('root')!);\nroot.render(<MinimalApp />);\n\nconsole.log('✅ 简化版本启动完成');\n"], "names": ["React", "require$$0", "REACT_ELEMENT_TYPE", "Symbol", "for", "REACT_PORTAL_TYPE", "REACT_FRAGMENT_TYPE", "REACT_STRICT_MODE_TYPE", "REACT_PROFILER_TYPE", "REACT_PROVIDER_TYPE", "REACT_CONTEXT_TYPE", "REACT_FORWARD_REF_TYPE", "REACT_SUSPENSE_TYPE", "REACT_SUSPENSE_LIST_TYPE", "REACT_MEMO_TYPE", "REACT_LAZY_TYPE", "REACT_OFFSCREEN_TYPE", "MAYBE_ITERATOR_SYMBOL", "iterator", "ReactSharedInternals", "__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED", "error", "format", "_len2", "arguments", "length", "args", "Array", "_key2", "level", "stack", "ReactDebugCurrentFrame", "getStackAddendum", "concat", "argsWithFormat", "map", "item", "String", "unshift", "Function", "prototype", "apply", "call", "console", "printWarning", "REACT_MODULE_REFERENCE", "getContextName", "type", "displayName", "getComponentNameFromType", "tag", "name", "$$typeof", "_context", "outerType", "innerType", "wrapperName", "functionName", "getWrappedName", "render", "outerName", "lazyComponent", "payload", "_payload", "init", "_init", "x", "prevLog", "prevInfo", "prev<PERSON>arn", "prevError", "prevGroup", "prevGroupCollapsed", "prevGroupEnd", "assign", "Object", "<PERSON><PERSON><PERSON><PERSON>", "disabledLog", "__reactDisabledLog", "prefix", "ReactCur<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "describeBuiltInComponentFrame", "source", "ownerFn", "Error", "match", "trim", "componentFrameCache", "reentry", "PossiblyWeakMap", "WeakMap", "Map", "describeNativeComponentFrame", "fn", "construct", "control", "frame", "get", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "previousPrepareStackTrace", "prepareStackTrace", "current", "log", "info", "warn", "group", "groupCollapsed", "groupEnd", "props", "configurable", "enumerable", "value", "writable", "defineProperties", "disableLogs", "Fake", "defineProperty", "set", "Reflect", "sample", "sampleLines", "split", "controlLines", "s", "c", "_frame", "replace", "includes", "reenableLogs", "syntheticFrame", "describeUnknownElementTypeFrameInDEV", "isReactComponent", "hasOwnProperty", "loggedTypeFailures", "setCurrentlyValidatingElement", "element", "owner", "_owner", "_source", "setExtraStackFrame", "isArrayImpl", "isArray", "a", "testStringCoercion", "checkKeyStringCoercion", "e", "willCoercionThrow", "toStringTag", "constructor", "typeName", "specialPropKeyWarningShown", "specialPropRefWarningShown", "didWarnAboutStringRefs", "ReactCurrentOwner", "RESERVED_PROPS", "key", "ref", "__self", "__source", "jsxDEV", "config", "<PERSON><PERSON><PERSON>", "self", "propName", "getter", "getOwnPropertyDescriptor", "isReactWarning", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hasValidRef", "stateNode", "componentName", "warnIfStringRefCannotBeAutoConverted", "defaultProps", "warnAboutAccessingKey", "defineKeyPropWarningGetter", "warnAboutAccessingRef", "defineRefPropWarningGetter", "_store", "freeze", "ReactElement", "propTypesMisspellWarningShown", "ReactCurrentOwner$1", "ReactDebugCurrentFrame$1", "setCurrentlyValidatingElement$1", "isValidElement", "object", "getDeclarationErrorAddendum", "ownerHasKeyUseWarning", "validateExplicitKey", "parentType", "validated", "currentComponentErrorInfo", "parentName", "getCurrentComponentErrorInfo", "childOwner", "validate<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "node", "i", "child", "iteratorFn", "maybeIterable", "maybeIterator", "getIteratorFn", "entries", "step", "next", "done", "validatePropTypes", "propTypes", "typeSpecs", "values", "location", "has", "bind", "typeSpecName", "error$1", "err", "ex", "message", "checkPropTypes", "PropTypes", "getDefaultProps", "isReactClassApproved", "didWarnAboutKeySpread", "jsxDEV$1", "isStaticChildren", "validType", "getModuleId", "isValidElementType", "keys", "typeString", "sourceInfo", "fileName", "lineNumber", "getSourceInfoErrorAddendum", "children", "filter", "k", "<PERSON><PERSON><PERSON><PERSON>", "join", "fragment", "validateFragmentProps", "reactJsxDevRuntime_development", "Fragment", "jsxDevRuntimeModule", "exports", "m", "client", "o", "usingClientEntryPoint", "createRoot", "hydrateRoot", "h", "SimpleTimestampConverter", "timestamp", "setTimestamp", "useState", "Math", "floor", "Date", "now", "dateInput", "setDateInput", "isDark", "setIsDark", "useEffect", "prefersDark", "window", "matchMedia", "matches", "toISOString", "slice", "document", "documentElement", "classList", "add", "remove", "formatDate", "ts", "toLocaleString", "timeZone", "year", "month", "day", "hour", "minute", "second", "className", "columnNumber", "this", "onClick", "onChange", "parseInt", "isNaN", "date", "handleTimestampChange", "target", "placeholder", "getTime", "handleDateChange", "MinimalApp", "StrictMode", "addEventListener", "event", "reason", "ReactDOM", "getElementById"], "mappings": "8yBAaE,WAGF,IAAIA,EAAQC,IAMRC,EAAqBC,OAAOC,IAAI,iBAChCC,EAAoBF,OAAOC,IAAI,gBAC/BE,EAAsBH,OAAOC,IAAI,kBACjCG,EAAyBJ,OAAOC,IAAI,qBACpCI,EAAsBL,OAAOC,IAAI,kBACjCK,EAAsBN,OAAOC,IAAI,kBACjCM,EAAqBP,OAAOC,IAAI,iBAChCO,EAAyBR,OAAOC,IAAI,qBACpCQ,EAAsBT,OAAOC,IAAI,kBACjCS,EAA2BV,OAAOC,IAAI,uBACtCU,EAAkBX,OAAOC,IAAI,cAC7BW,EAAkBZ,OAAOC,IAAI,cAC7BY,EAAuBb,OAAOC,IAAI,mBAClCa,EAAwBd,OAAOe,SAgBnC,IAAIC,EAAuBnB,EAAMoB,mDAEjC,SAASC,EAAMC,GAGT,IAAA,IAASC,EAAQC,UAAUC,OAAQC,EAAO,IAAIC,MAAMJ,EAAQ,EAAIA,EAAQ,EAAI,GAAIK,EAAQ,EAAGA,EAAQL,EAAOK,IACxGF,EAAKE,EAAQ,GAAKJ,UAAUI,IAQpC,SAAsBC,EAAOP,EAAQI,GAIjC,IACII,EADyBX,EAAqBY,uBACfC,mBAErB,KAAVF,IACFR,GAAU,KACVI,EAAOA,EAAKO,OAAO,CAACH,KAItB,IAAII,EAAiBR,EAAKS,IAAI,SAAUC,GACtC,OAAOC,OAAOD,EAAI,GAGpBF,EAAeI,QAAQ,YAAchB,GAIrCiB,SAASC,UAAUC,MAAMC,KAAKC,QAAQd,GAAQc,QAAST,EACzD,CA3BIU,CAAa,QAAStB,EAAQI,EAElC,CA8BF,IAUImB,EAyCJ,SAASC,EAAeC,GACtB,OAAOA,EAAKC,aAAe,SAAA,CAI7B,SAASC,EAAyBF,GAChC,GAAY,MAARA,EAEF,OAAO,KAST,GAL0B,iBAAbA,EAAKG,KACd7B,EAAM,qHAIU,mBAAT0B,EACT,OAAOA,EAAKC,aAAeD,EAAKI,MAAQ,KAG1C,GAAoB,iBAATJ,EACT,OAAOA,EAGT,OAAQA,GACN,KAAKzC,EACH,MAAO,WAET,KAAKD,EACH,MAAO,SAET,KAAKG,EACH,MAAO,WAET,KAAKD,EACH,MAAO,aAET,KAAKK,EACH,MAAO,WAET,KAAKC,EACH,MAAO,eAIX,GAAoB,iBAATkC,EACT,OAAQA,EAAKK,UACX,KAAK1C,EAEH,OAAOoC,EADOC,GACmB,YAEnC,KAAKtC,EAEH,OAAOqC,EADQC,EACgBM,UAAY,YAE7C,KAAK1C,EACH,OArER,SAAwB2C,EAAWC,EAAWC,GAC5C,IAAIR,EAAcM,EAAUN,YAE5B,GAAIA,EACF,OAAOA,EAGT,IAAIS,EAAeF,EAAUP,aAAeO,EAAUJ,MAAQ,GAC9D,MAAwB,KAAjBM,EAAsBD,EAAc,IAAMC,EAAe,IAAMD,CAAA,CA6DzDE,CAAeX,EAAMA,EAAKY,OAAQ,cAE3C,KAAK7C,EACH,IAAI8C,EAAYb,EAAKC,aAAe,KAEpC,OAAkB,OAAdY,EACKA,EAGFX,EAAyBF,EAAKA,OAAS,OAEhD,KAAKhC,EAED,IAAI8C,EAAgBd,EAChBe,EAAUD,EAAcE,SACxBC,EAAOH,EAAcI,MAEzB,IACE,OAAOhB,EAAyBe,EAAKF,GAAQ,OACtCI,GACP,OAAO,IAAA,EAQjB,OAAO,IAAA,CA3HPrB,EAAyB1C,OAAOC,IAAI,0BA8HtC,IAOI+D,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EAbAC,EAASC,OAAOD,OAMhBE,EAAgB,EASpB,SAASC,IAAc,CAEvBA,EAAYC,oBAAqB,EA+EjC,IACIC,EADAC,EAAyB7D,EAAqB6D,uBAElD,SAASC,EAA8B9B,EAAM+B,EAAQC,GAEjD,QAAe,IAAXJ,EAEF,IACE,MAAMK,OAAM,OACLlB,GACP,IAAImB,EAAQnB,EAAEpC,MAAMwD,OAAOD,MAAM,gBACjCN,EAASM,GAASA,EAAM,IAAM,EAAA,CAKlC,MAAO,KAAON,EAAS5B,CACzB,CAEF,IACIoC,EADAC,GAAU,EAIRC,EAAqC,mBAAZC,QAAyBA,QAAUC,IAIlE,SAASC,EAA6BC,EAAIC,GAExC,IAAMD,GAAML,EACV,MAAO,GAIP,IAOEO,EAPEC,EAAQT,EAAoBU,IAAIJ,GAEpC,QAAc,IAAVG,EACF,OAAOA,EAKXR,GAAU,EACV,IAGIU,EAHAC,EAA4Bf,MAAMgB,kBAEtChB,MAAMgB,uBAAoB,EAIxBF,EAAqBlB,EAAuBqB,QAG5CrB,EAAuBqB,QAAU,KAjIrC,WAEI,GAAsB,IAAlBzB,EAAqB,CAEvBT,EAAUxB,QAAQ2D,IAClBlC,EAAWzB,QAAQ4D,KACnBlC,EAAW1B,QAAQ6D,KACnBlC,EAAY3B,QAAQtB,MACpBkD,EAAY5B,QAAQ8D,MACpBjC,EAAqB7B,QAAQ+D,eAC7BjC,EAAe9B,QAAQgE,SAEvB,IAAIC,EAAQ,CACVC,cAAc,EACdC,YAAY,EACZC,MAAOlC,EACPmC,UAAU,GAGZrC,OAAOsC,iBAAiBtE,QAAS,CAC/B4D,KAAMK,EACNN,IAAKM,EACLJ,KAAMI,EACNvF,MAAOuF,EACPH,MAAOG,EACPF,eAAgBE,EAChBD,SAAUC,GACX,CAIHhC,GACF,CAkGEsC,GAGF,IAEE,GAAIpB,EAAW,CAEb,IAAIqB,EAAO,WACT,MAAM/B,OAAM,EAYd,GARAT,OAAOyC,eAAeD,EAAK3E,UAAW,QAAS,CAC7C6E,IAAK,WAGH,MAAMjC,OAAM,IAIO,iBAAZkC,SAAwBA,QAAQxB,UAAW,CAGpD,IACEwB,QAAQxB,UAAUqB,EAAM,GAAE,OACnBjD,GACP6B,EAAU7B,CAAA,CAGZoD,QAAQxB,UAAUD,EAAI,GAAIsB,EAAI,KACzB,CACL,IACEA,EAAKzE,MAAK,OACHwB,GACP6B,EAAU7B,CAAA,CAGZ2B,EAAGnD,KAAKyE,EAAK3E,UAAS,CACxB,KACK,CACL,IACE,MAAM4C,OAAM,OACLlB,GACP6B,EAAU7B,CAAA,CAGZ2B,GAAG,CACL,OACO0B,GAEP,GAAIA,GAAUxB,GAAmC,iBAAjBwB,EAAOzF,MAAoB,CAQzD,IALA,IAAI0F,EAAcD,EAAOzF,MAAM2F,MAAM,MACjCC,EAAe3B,EAAQjE,MAAM2F,MAAM,MACnCE,EAAIH,EAAY/F,OAAS,EACzBmG,EAAIF,EAAajG,OAAS,EAEvBkG,GAAK,GAAKC,GAAK,GAAKJ,EAAYG,KAAOD,EAAaE,IAOzDA,IAGF,KAAOD,GAAK,GAAKC,GAAK,EAAGD,IAAKC,IAG5B,GAAIJ,EAAYG,KAAOD,EAAaE,GAAI,CAMtC,GAAU,IAAND,GAAiB,IAANC,EACb,GAKE,GAJAD,MACAC,EAGQ,GAAKJ,EAAYG,KAAOD,EAAaE,GAAI,CAE/C,IAAIC,EAAS,KAAOL,EAAYG,GAAGG,QAAQ,WAAY,QAgBvD,OAXIjC,EAAG7C,aAAe6E,EAAOE,SAAS,iBACpCF,EAASA,EAAOC,QAAQ,cAAejC,EAAG7C,cAIxB,mBAAP6C,GACTN,EAAoB8B,IAAIxB,EAAIgC,GAKzBA,CAAA,QAEFF,GAAK,GAAKC,GAAK,GAG1B,KAAA,CAEJ,CACF,CAAA,QAEApC,GAAU,EAGRR,EAAuBqB,QAAUH,EAlNvC,WAII,GAAsB,MAFtBtB,EAEyB,CAEvB,IAAIgC,EAAQ,CACVC,cAAc,EACdC,YAAY,EACZE,UAAU,GAGZrC,OAAOsC,iBAAiBtE,QAAS,CAC/B2D,IAAK5B,EAAO,CAAA,EAAIkC,EAAO,CACrBG,MAAO5C,IAEToC,KAAM7B,EAAO,CAAA,EAAIkC,EAAO,CACtBG,MAAO3C,IAEToC,KAAM9B,EAAO,CAAA,EAAIkC,EAAO,CACtBG,MAAO1C,IAEThD,MAAOqD,EAAO,CAAA,EAAIkC,EAAO,CACvBG,MAAOzC,IAETmC,MAAO/B,EAAO,CAAA,EAAIkC,EAAO,CACvBG,MAAOxC,IAETmC,eAAgBhC,EAAO,CAAA,EAAIkC,EAAO,CAChCG,MAAOvC,IAETmC,SAAUjC,EAAO,CAAA,EAAIkC,EAAO,CAC1BG,MAAOtC,KAEV,CAICG,EAAgB,GAClBvD,EAAM,+EAEV,CA0KI2G,GAGF5C,MAAMgB,kBAAoBD,CAAA,CAI5B,IAAIhD,EAAO0C,EAAKA,EAAG7C,aAAe6C,EAAG1C,KAAO,GACxC8E,EAAiB9E,EAAO8B,EAA8B9B,GAAQ,GAQlE,MALoB,mBAAP0C,GACTN,EAAoB8B,IAAIxB,EAAIoC,GAIzBA,CAAA,CAaT,SAASC,EAAqCnF,EAAMmC,EAAQC,GAE1D,GAAY,MAARpC,EACF,MAAO,GAGT,GAAoB,mBAATA,EAEP,OAAO6C,EAA6B7C,MAZpCP,EAY0DO,EAZpCP,aACHA,EAAU2F,mBAFnC,IACM3F,EAgBJ,GAAoB,iBAATO,EACT,OAAOkC,EAA8BlC,GAGvC,OAAQA,GACN,KAAKnC,EACH,OAAOqE,EAA8B,YAEvC,KAAKpE,EACH,OAAOoE,EAA8B,gBAGzC,GAAoB,iBAATlC,EACT,OAAQA,EAAKK,UACX,KAAKzC,EACH,OApCGiF,EAoCmC7C,EAAKY,QApCP,GAsCtC,KAAK7C,EAEH,OAAOoH,EAAqCnF,EAAKA,KAAMmC,EAAQC,GAEjE,KAAKpE,EAED,IAAI8C,EAAgBd,EAChBe,EAAUD,EAAcE,SACxBC,EAAOH,EAAcI,MAEzB,IAEE,OAAOiE,EAAqClE,EAAKF,GAAUoB,EAAQC,EAAO,OACnEjB,GAAG,EAKpB,MAAO,EAAA,CA5NPqB,EAAsB,IAAIE,EA+N5B,IAAI2C,EAAiBzD,OAAOnC,UAAU4F,eAElCC,EAAqB,CAAA,EACrBtG,EAAyBZ,EAAqBY,uBAElD,SAASuG,EAA8BC,GAEnC,GAAIA,EAAS,CACX,IAAIC,EAAQD,EAAQE,OAChB3G,EAAQoG,EAAqCK,EAAQxF,KAAMwF,EAAQG,QAASF,EAAQA,EAAMzF,KAAO,MACrGhB,EAAuB4G,mBAAmB7G,EAAK,MAE/CC,EAAuB4G,mBAAmB,KAE9C,CAoDF,IAAIC,EAAcjH,MAAMkH,QAExB,SAASA,EAAQC,GACf,OAAOF,EAAYE,EAAC,CAkCtB,SAASC,EAAmBhC,GAwB1B,MAAO,GAAKA,CAAA,CAEd,SAASiC,EAAuBjC,GAE5B,GAvCJ,SAA2BA,GAEvB,IAEE,OADAgC,EAAmBhC,IACZ,CAAA,OACAkC,GACP,OAAO,CAAA,CAEX,CA+BMC,CAAkBnC,GAGpB,OAFA1F,EAAM,kHAlDZ,SAAkB0F,GAKd,MAFuC,mBAAX5G,QAAyBA,OAAOgJ,aAC/BpC,EAAM5G,OAAOgJ,cAAgBpC,EAAMqC,YAAYjG,MAAQ,QAEtF,CA4CkIkG,CAAStC,IAEhIgC,EAAmBhC,EAE9B,CAGF,IAOIuC,EACAC,EACAC,EATAC,EAAoBtI,EAAqBsI,kBACzCC,EAAiB,CACnBC,KAAK,EACLC,KAAK,EACLC,QAAQ,EACRC,UAAU,GAOVN,EAAyB,CAAA,EAkK3B,SAASO,EAAOhH,EAAMiH,EAAQC,EAAU/E,EAAQgF,GAE5C,IAAIC,EAEAvD,EAAQ,CAAA,EACR+C,EAAM,KACNC,EAAM,KA6BV,IAAKO,UAtBY,IAAbF,IAEAjB,EAAuBiB,GAGzBN,EAAM,GAAKM,GAnKjB,SAAqBD,GAEjB,GAAI5B,EAAe1F,KAAKsH,EAAQ,OAAQ,CACtC,IAAII,EAASzF,OAAO0F,yBAAyBL,EAAQ,OAAO/D,IAE5D,GAAImE,GAAUA,EAAOE,eACnB,OAAO,CACT,CAIJ,YAAsB,IAAfN,EAAOL,GAAQ,CA2JhBY,CAAYP,KAEZhB,EAAuBgB,EAAOL,KAGhCA,EAAM,GAAKK,EAAOL,KAzLxB,SAAqBK,GAEjB,GAAI5B,EAAe1F,KAAKsH,EAAQ,OAAQ,CACtC,IAAII,EAASzF,OAAO0F,yBAAyBL,EAAQ,OAAO/D,IAE5D,GAAImE,GAAUA,EAAOE,eACnB,OAAO,CACT,CAIJ,YAAsB,IAAfN,EAAOJ,GAAQ,CAiLhBY,CAAYR,KACdJ,EAAMI,EAAOJ,IAjKnB,SAA8CI,EAAQE,GAElD,GAA0B,iBAAfF,EAAOJ,KAAoBH,EAAkBpD,SAAW6D,GAAQT,EAAkBpD,QAAQoE,YAAcP,EAAM,CACvH,IAAIQ,EAAgBzH,EAAyBwG,EAAkBpD,QAAQtD,MAElEyG,EAAuBkB,KAC1BrJ,EAAM,4VAAsX4B,EAAyBwG,EAAkBpD,QAAQtD,MAAOiH,EAAOJ,KAE7bJ,EAAuBkB,IAAiB,EAC1C,CAEJ,CAuJIC,CAAqCX,EAAQE,IAI9BF,EACX5B,EAAe1F,KAAKsH,EAAQG,KAAcT,EAAetB,eAAe+B,KAC1EvD,EAAMuD,GAAYH,EAAOG,IAK7B,GAAIpH,GAAQA,EAAK6H,aAAc,CAC7B,IAAIA,EAAe7H,EAAK6H,aAExB,IAAKT,KAAYS,OACS,IAApBhE,EAAMuD,KACRvD,EAAMuD,GAAYS,EAAaT,GAEnC,CAGF,GAAIR,GAAOC,EAAK,CACd,IAAI5G,EAA8B,mBAATD,EAAsBA,EAAKC,aAAeD,EAAKI,MAAQ,UAAYJ,EAExF4G,GA5KV,SAAoC/C,EAAO5D,GAEvC,IAAI6H,EAAwB,WACrBvB,IACHA,GAA6B,EAE7BjI,EAAM,4OAA4P2B,GACpQ,EAGF6H,EAAsBP,gBAAiB,EACvC3F,OAAOyC,eAAeR,EAAO,MAAO,CAClCX,IAAK4E,EACLhE,cAAc,GAElB,CA8JMiE,CAA2BlE,EAAO5D,GAGhC4G,GA9JV,SAAoChD,EAAO5D,GAEvC,IAAI+H,EAAwB,WACrBxB,IACHA,GAA6B,EAE7BlI,EAAM,4OAA4P2B,GACpQ,EAGF+H,EAAsBT,gBAAiB,EACvC3F,OAAOyC,eAAeR,EAAO,MAAO,CAClCX,IAAK8E,EACLlE,cAAc,GAElB,CAgJMmE,CAA2BpE,EAAO5D,EACpC,CAGF,OA5He,SAAUD,EAAM4G,EAAKC,EAAKM,EAAMhF,EAAQsD,EAAO5B,GAChE,IAAI2B,EAAU,CAEZnF,SAAUlD,EAEV6C,OACA4G,MACAC,MACAhD,QAEA6B,OAAQD,EAQRD,OAAiB,CAAA,GAiCnB,OA5BE5D,OAAOyC,eAAemB,EAAQ0C,OAAQ,YAAa,CACjDpE,cAAc,EACdC,YAAY,EACZE,UAAU,EACVD,OAAO,IAGTpC,OAAOyC,eAAemB,EAAS,QAAS,CACtC1B,cAAc,EACdC,YAAY,EACZE,UAAU,EACVD,MAAOmD,IAITvF,OAAOyC,eAAemB,EAAS,UAAW,CACxC1B,cAAc,EACdC,YAAY,EACZE,UAAU,EACVD,MAAO7B,IAGLP,OAAOuG,SACTvG,OAAOuG,OAAO3C,EAAQ3B,OACtBjC,OAAOuG,OAAO3C,IAIXA,CAAA,CAyEE4C,CAAapI,EAAM4G,EAAKC,EAAKM,EAAMhF,EAAQuE,EAAkBpD,QAASO,EAC/E,CAGF,IAeIwE,GAfAC,GAAsBlK,EAAqBsI,kBAC3C6B,GAA2BnK,EAAqBY,uBAEpD,SAASwJ,GAAgChD,GAErC,GAAIA,EAAS,CACX,IAAIC,EAAQD,EAAQE,OAChB3G,EAAQoG,EAAqCK,EAAQxF,KAAMwF,EAAQG,QAASF,EAAQA,EAAMzF,KAAO,MACrGuI,GAAyB3C,mBAAmB7G,EAAK,MAEjDwJ,GAAyB3C,mBAAmB,KAEhD,CAiBF,SAAS6C,GAAeC,GAEpB,MAAyB,iBAAXA,GAAkC,OAAXA,GAAmBA,EAAOrI,WAAalD,CAC9E,CAGF,SAASwL,KAEL,GAAIL,GAAoBhF,QAAS,CAC/B,IAAIlD,EAAOF,EAAyBoI,GAAoBhF,QAAQtD,MAEhE,GAAII,EACF,MAAO,mCAAqCA,EAAO,IACrD,CAGF,MAAO,EACT,CA5BAiI,IAAgC,EAiDlC,IAAIO,GAAwB,CAAA,EA8B5B,SAASC,GAAoBrD,EAASsD,GAElC,GAAKtD,EAAQ0C,SAAU1C,EAAQ0C,OAAOa,WAA4B,MAAfvD,EAAQoB,IAA3D,CAIApB,EAAQ0C,OAAOa,WAAY,EAC3B,IAAIC,EAnCR,SAAsCF,GAElC,IAAItF,EAAOmF,KAEX,IAAKnF,EAAM,CACT,IAAIyF,EAAmC,iBAAfH,EAA0BA,EAAaA,EAAW7I,aAAe6I,EAAW1I,KAEhG6I,IACFzF,EAAO,8CAAgDyF,EAAa,KACtE,CAGF,OAAOzF,CACT,CAsBkC0F,CAA6BJ,GAE7D,IAAIF,GAAsBI,GAA1B,CAIAJ,GAAsBI,IAA6B,EAInD,IAAIG,EAAa,GAEb3D,GAAWA,EAAQE,QAAUF,EAAQE,SAAW4C,GAAoBhF,UAEtE6F,EAAa,+BAAiCjJ,EAAyBsF,EAAQE,OAAO1F,MAAQ,KAGhGwI,GAAgChD,GAEhClH,EAAM,4HAAkI0K,EAA2BG,GAEnKX,GAAgC,KAlB9B,CAPA,CA0BJ,CAaF,SAASY,GAAkBC,EAAMP,GAE7B,GAAoB,iBAATO,EAIX,GAAIvD,EAAQuD,GACV,IAAA,IAASC,EAAI,EAAGA,EAAID,EAAK3K,OAAQ4K,IAAK,CACpC,IAAIC,EAAQF,EAAKC,GAEbb,GAAec,IACjBV,GAAoBU,EAAOT,EAC7B,MACF,GACSL,GAAeY,GAEpBA,EAAKnB,SACPmB,EAAKnB,OAAOa,WAAY,WAEjBM,EAAM,CACf,IAAIG,EApjCV,SAAuBC,GACrB,GAAsB,OAAlBA,GAAmD,iBAAlBA,EACnC,OAAO,KAGT,IAAIC,EAAgBxL,GAAyBuL,EAAcvL,IAA0BuL,EAN5D,cAQzB,MAA6B,mBAAlBC,EACFA,EAGF,IAAA,CAyiCcC,CAAcN,GAE/B,GAA0B,mBAAfG,GAGLA,IAAeH,EAAKO,QAItB,IAHA,IACIC,EADA1L,EAAWqL,EAAW7J,KAAK0J,KAGtBQ,EAAO1L,EAAS2L,QAAQC,MAC3BtB,GAAeoB,EAAK7F,QACtB6E,GAAoBgB,EAAK7F,MAAO8E,EAIxC,CAEJ,CAUF,SAASkB,GAAkBxE,GAEvB,IAMIyE,EANAjK,EAAOwF,EAAQxF,KAEnB,GAAIA,SAAuD,iBAATA,EAAlD,CAMA,GAAoB,mBAATA,EACTiK,EAAYjK,EAAKiK,cAAA,IACQ,iBAATjK,GAAsBA,EAAKK,WAAazC,GAE1DoC,EAAKK,WAAatC,EAGhB,OAFAkM,EAAYjK,EAAKiK,SAEjB,CAGF,GAAIA,EAAW,CAEb,IAAI7J,EAAOF,EAAyBF,IA5jB1C,SAAwBkK,EAAWC,EAAQC,EAAUzC,EAAenC,GAGhE,IAAI6E,EAAM7K,SAASG,KAAK2K,KAAKjF,GAE7B,IAAA,IAASkF,KAAgBL,EACvB,GAAIG,EAAIH,EAAWK,GAAe,CAChC,IAAIC,OAAU,EAId,IAGE,GAAuC,mBAA5BN,EAAUK,GAA8B,CAEjD,IAAIE,EAAMpI,OAAOsF,GAAiB,eAAiB,KAAOyC,EAAW,UAAYG,EAAe,oGAA2GL,EAAUK,GAAgB,mGAErO,MADAE,EAAIrK,KAAO,sBACLqK,CAAA,CAGRD,EAAUN,EAAUK,GAAcJ,EAAQI,EAAc5C,EAAeyC,EAAU,KAAM,+CAA8C,OAC9HM,GACPF,EAAUE,CAAA,EAGRF,GAAaA,aAAmBnI,QAClCkD,EAA8BC,GAE9BlH,EAAM,2RAAqTqJ,GAAiB,cAAeyC,EAAUG,SAAqBC,GAE1XjF,EAA8B,OAG5BiF,aAAmBnI,SAAWmI,EAAQG,WAAWrF,KAGnDA,EAAmBkF,EAAQG,UAAW,EACtCpF,EAA8BC,GAE9BlH,EAAM,qBAAsB8L,EAAUI,EAAQG,SAE9CpF,EAA8B,MAChC,CAGN,CA+gBIqF,CAAeX,EAAWzE,EAAQ3B,MAAO,OAAQzD,EAAMoF,EAAO,MAAA,QAClC,IAAnBxF,EAAK6K,YAA4BxC,GAA+B,CACzEA,IAAgC,EAIhC/J,EAAM,sGAFM4B,EAAyBF,IAEiF,UAAS,CAG7F,mBAAzBA,EAAK8K,iBAAmC9K,EAAK8K,gBAAgBC,sBACtEzM,EAAM,6HA5BN,CA8BJ,CAmCF,IAAI0M,GAAwB,CAAA,EAmG5B,IAAIC,GAlGJ,SAA2BjL,EAAM6D,EAAO+C,EAAKsE,EAAkB/I,EAAQgF,GAEnE,IAAIgE,EAjlCR,SAA4BnL,GAC1B,MAAoB,iBAATA,GAAqC,mBAATA,GAKnCA,IAASzC,GAAuByC,IAASvC,GAA8CuC,IAASxC,GAA0BwC,IAASnC,GAAuBmC,IAASlC,GAAmDkC,IAAS/B,GAI/M,iBAAT+B,GAA8B,OAATA,IAC1BA,EAAKK,WAAarC,GAAmBgC,EAAKK,WAAatC,GAAmBiC,EAAKK,WAAa3C,GAAuBsC,EAAKK,WAAa1C,GAAsBqC,EAAKK,WAAazC,GAIjLoC,EAAKK,WAAaP,QAA+C,IAArBE,EAAKoL,YAK5C,CA6jCWC,CAAmBrL,GAGnC,IAAKmL,EAAW,CACd,IAAI3H,EAAO,SAEE,IAATxD,GAAsC,iBAATA,GAA8B,OAATA,GAA8C,IAA7B4B,OAAO0J,KAAKtL,GAAMtB,UACvF8E,GAAQ,oIAGV,IAQI+H,EARAC,EA5NV,SAAoCrJ,GAEhC,YAAe,IAAXA,EAGK,0BAFQA,EAAOsJ,SAAS1G,QAAQ,YAAa,IAEN,IAD7B5C,EAAOuJ,WACyC,IAG5D,EACT,CAmNqBC,CAA2BxJ,GAG1CqB,GADEgI,GAGM7C,KAKG,OAAT3I,EACFuL,EAAa,OACJzF,EAAQ9F,GACjBuL,EAAa,aACK,IAATvL,GAAsBA,EAAKK,WAAalD,GACjDoO,EAAa,KAAOrL,EAAyBF,EAAKA,OAAS,WAAa,MACxEwD,EAAO,sEAEP+H,SAAoBvL,EAGtB1B,EAAM,0IAAqJiN,EAAY/H,EAAI,CAG7K,IAAIgC,EAAUwB,EAAOhH,EAAM6D,EAAO+C,EAAKzE,EAAQgF,GAG/C,GAAe,MAAX3B,EACF,OAAOA,EAQT,GAAI2F,EAAW,CACb,IAAIS,EAAW/H,EAAM+H,SAErB,QAAiB,IAAbA,EACF,GAAIV,EACF,GAAIpF,EAAQ8F,GAAW,CACrB,IAAA,IAAStC,EAAI,EAAGA,EAAIsC,EAASlN,OAAQ4K,IACnCF,GAAkBwC,EAAStC,GAAItJ,GAG7B4B,OAAOuG,QACTvG,OAAOuG,OAAOyD,EAChB,MAEAtN,EAAM,6JAGR8K,GAAkBwC,EAAU5L,EAEhC,CAIA,GAAIqF,EAAe1F,KAAKkE,EAAO,OAAQ,CACrC,IAAI8D,EAAgBzH,EAAyBF,GACzCsL,EAAO1J,OAAO0J,KAAKzH,GAAOgI,OAAO,SAAUC,GAC7C,MAAa,QAANA,CAAM,GAEXC,EAAgBT,EAAK5M,OAAS,EAAI,kBAAoB4M,EAAKU,KAAK,WAAa,SAAW,iBAE5F,IAAKhB,GAAsBrD,EAAgBoE,GAGzCzN,EAAM,kOAA4PyN,EAAepE,EAF9P2D,EAAK5M,OAAS,EAAI,IAAM4M,EAAKU,KAAK,WAAa,SAAW,KAEiOrE,GAE9SqD,GAAsBrD,EAAgBoE,IAAiB,CACzD,CAUJ,OANI/L,IAASzC,EApHjB,SAA+B0O,GAI3B,IAFA,IAAIX,EAAO1J,OAAO0J,KAAKW,EAASpI,OAEvByF,EAAI,EAAGA,EAAIgC,EAAK5M,OAAQ4K,IAAK,CACpC,IAAI1C,EAAM0E,EAAKhC,GAEf,GAAY,aAAR1C,GAA8B,QAARA,EAAe,CACvC4B,GAAgCyD,GAEhC3N,EAAM,2GAAiHsI,GAEvH4B,GAAgC,MAChC,KAAA,CACF,CAGmB,OAAjByD,EAASpF,MACX2B,GAAgCyD,GAEhC3N,EAAM,yDAENkK,GAAgC,MAEpC,CA6FI0D,CAAsB1G,GAEtBwE,GAAkBxE,GAGbA,CACT,EAKF2G,EAAAC,SAAmB7O,EACnB4O,EAAAnF,OAAiBiE,EAAA,CAnxCf,uBCRAoB,EAAAC,QAAiBpP,4DCHnB,IAAIqP,EAAIrP,IAKFoM,EAAIiD,EAAElO,0DACVmO,aAAqB,SAAS3H,EAAG4H,GAC/BnD,EAAEoD,uBAAwB,EAC1B,IACE,OAAOH,EAAEI,WAAW9H,EAAG4H,EAAC,CAAA,QAExBnD,EAAEoD,uBAAwB,CAAA,CAC5B,EAEFF,EAAAI,YAAsB,SAAS/H,EAAGgI,EAAGJ,GACnCnD,EAAEoD,uBAAwB,EAC1B,IACE,OAAOH,EAAEK,YAAY/H,EAAGgI,EAAGJ,EAAC,CAAA,QAE5BnD,EAAEoD,uBAAwB,CAAA,CAC5B,+BClBJ,SAASI,IACP,MAAOC,EAAWC,GAAgBC,WAASC,KAAKC,MAAMC,KAAKC,MAAQ,OAC5DC,EAAWC,GAAgBN,EAAAA,SAAS,KACpCO,EAAQC,GAAaR,EAAAA,UAAS,GAErCS,EAAAA,UAAU,KAER,MAAMC,EAAcC,OAAOC,WAAW,gCAAgCC,QACtEL,EAAUE,GAIVJ,OADgBH,MACCW,cAAcC,MAAM,EAAG,MACvC,IAEHN,EAAAA,UAAU,KAEJF,EACFS,SAASC,gBAAgBC,UAAUC,IAAI,QAEvCH,SAASC,gBAAgBC,UAAUE,OAAO,SAE3C,CAACb,IAEJ,MAuBMc,EAAcC,IAClB,IACE,OAAO,IAAInB,KAAU,IAALmB,GAAWC,eAAe,QAAS,CACjDC,SAAU,gBACVC,KAAM,UACNC,MAAO,UACPC,IAAK,UACLC,KAAM,UACNC,OAAQ,UACRC,OAAQ,WAEZ,OAASzQ,GACP,OAAO,IAAI8O,KAAU,IAALmB,GAAWC,gBAC7B,GAGF,gBACG,MAAA,CAAIQ,UAAW,oDACdxB,EAAS,yBAA2B,4DAGpC5B,SAAA,GAAA5E,OAAC,SAAA,CAAOgI,UAAU,mBAChBpD,SAAA,CAAA5E,SAAC,KAAA,CAAGgI,UAAU,0BAA0BpD,SAAA,eAAxC,GAAA,EAAA,CAAAH,SAAA,sDAAAC,WAAA,GAAAuD,aAAA,GAAAC,MACAlI,SAAC,IAAA,CAAEgI,UAAU,qBAAqBpD,SAAA,2BAAlC,GAAA,EAAA,CAAAH,SAAA,sDAAAC,WAAA,GAAAuD,aAAA,GAAAC,MACAlI,EAAAA,OAAC,SAAA,CACCmI,QAAS,IAAM1B,GAAWD,GAC1BwB,UAAU,mGAETpD,WAAS,UAAY,gBAJxB,GAAA,EAAA,CAAAH,SAAA,sDAAAC,WAAA,GAAAuD,aAAA,GAAAC,aAHF,GAAA,EAAA,CAAAzD,SAAA,sDAAAC,WAAA,GAAAuD,aAAA,GAAAC,QAYAlI,OAAC,MAAA,CAAIgI,UAAU,oBAEbpD,SAAA,GAAA5E,OAAC,MAAA,CAAIgI,UAAU,oGACbpD,SAAA,CAAA5E,SAAC,KAAA,CAAGgI,UAAU,6BAA6BpD,SAAA,aAA3C,GAAA,EAAA,CAAAH,SAAA,sDAAAC,WAAA,GAAAuD,aAAA,IAAAC,QACAlI,OAAC,MAAA,CAAIgI,UAAU,wCACbpD,SAAA,CAAA5E,SAAC,MAAA,CACC4E,SAAA,CAAA5E,SAAC,IAAA,CAAEgI,UAAU,0BAA0BpD,SAAA,YAAvC,GAAA,EAAA,CAAAH,SAAA,sDAAAC,WAAA,GAAAuD,aAAA,IAAAC,QACAlI,OAAC,IAAA,CAAEgI,UAAU,qBAAsBpD,SAAAsB,KAAKC,MAAMC,KAAKC,MAAQ,WAA3D,GAAA,EAAA,CAAA5B,SAAA,sDAAAC,WAAA,GAAAuD,aAAA,IAAAC,aAFF,GAAA,EAAA,CAAAzD,SAAA,sDAAAC,WAAA,GAAAuD,aAAA,IAAAC,eAIC,MAAA,CACCtD,SAAA,CAAA5E,SAAC,IAAA,CAAEgI,UAAU,0BAA0BpD,SAAA,aAAvC,GAAA,EAAA,CAAAH,SAAA,sDAAAC,WAAA,GAAAuD,aAAA,IAAAC,QACAlI,OAAC,IAAA,CAAEgI,UAAU,UAAWpD,SAAA0C,EAAWpB,KAAKC,MAAMC,KAAKC,MAAQ,YAA3D,GAAA,EAAA,CAAA5B,SAAA,sDAAAC,WAAA,GAAAuD,aAAA,IAAAC,aAFF,GAAA,EAAA,CAAAzD,SAAA,sDAAAC,WAAA,GAAAuD,aAAA,IAAAC,aALF,GAAA,EAAA,CAAAzD,SAAA,sDAAAC,WAAA,GAAAuD,aAAA,IAAAC,MAUAlI,EAAAA,OAAC,SAAA,CACCmI,QAtDU,KAClB,MAAM9B,EAAMH,KAAKC,MAAMC,KAAKC,MAAQ,KACpCL,EAAaK,GACbE,GAAA,IAAiBH,MAAOW,cAAcC,MAAM,EAAG,MAoDvCgB,UAAU,yFACXpD,SAAA,gBAHD,GAAA,EAAA,CAAAH,SAAA,sDAAAC,WAAA,GAAAuD,aAAA,IAAAC,aAZF,GAAA,EAAA,CAAAzD,SAAA,sDAAAC,WAAA,GAAAuD,aAAA,GAAAC,QAqBAlI,OAAC,MAAA,CAAIgI,UAAU,wCAEbpD,SAAA,GAAA5E,OAAC,MAAA,CAAIgI,UAAU,+FACbpD,SAAA,CAAA5E,SAAC,KAAA,CAAGgI,UAAU,6BAA6BpD,SAAA,eAA3C,GAAA,EAAA,CAAAH,SAAA,sDAAAC,WAAA,IAAAuD,aAAA,IAAAC,MACAlI,EAAAA,OAAC,QAAA,CACChH,KAAK,SACLgE,MAAO+I,EACPqC,SAAWlJ,GAtFO,CAAClC,IAC7B,MAAMuK,EAAKc,SAASrL,GACpB,IAAKsL,MAAMf,IAAOA,EAAK,EAAG,CACxBvB,EAAauB,GACb,MAAMgB,EAAO,IAAInC,KAAU,IAALmB,GACtBhB,EAAagC,EAAKxB,cAAcC,MAAM,EAAG,IAC3C,GAgF2BwB,CAAsBtJ,EAAEuJ,OAAOzL,OAChDgL,UAAU,yIACVU,YAAY,cALd,GAAA,EAAA,CAAAjE,SAAA,sDAAAC,WAAA,IAAAuD,aAAA,IAAAC,QAOAlI,OAAC,MAAA,CAAIgI,UAAU,6CACbpD,SAAA,CAAA5E,SAAC,IAAA,CAAEgI,UAAU,0BAA0BpD,SAAA,aAAvC,GAAA,EAAA,CAAAH,SAAA,sDAAAC,WAAA,IAAAuD,aAAA,IAAAC,eACC,IAAA,CAAEF,UAAU,YAAapD,SAAA0C,EAAWvB,SAArC,GAAA,EAAA,CAAAtB,SAAA,sDAAAC,WAAA,IAAAuD,aAAA,IAAAC,aAFF,GAAA,EAAA,CAAAzD,SAAA,sDAAAC,WAAA,IAAAuD,aAAA,IAAAC,aATF,GAAA,EAAA,CAAAzD,SAAA,sDAAAC,WAAA,IAAAuD,aAAA,IAAAC,QAgBAlI,OAAC,MAAA,CAAIgI,UAAU,+FACbpD,SAAA,CAAA5E,SAAC,KAAA,CAAGgI,UAAU,6BAA6BpD,SAAA,eAA3C,GAAA,EAAA,CAAAH,SAAA,sDAAAC,WAAA,IAAAuD,aAAA,IAAAC,MACAlI,EAAAA,OAAC,QAAA,CACChH,KAAK,iBACLgE,MAAOsJ,EACP8B,SAAWlJ,GA7FE,CAAClC,IACxBuJ,EAAavJ,GACb,MAAMuL,EAAO,IAAInC,KAAKpJ,GACjBsL,MAAMC,EAAKI,YACd3C,EAAaE,KAAKC,MAAMoC,EAAKI,UAAY,OAyFhBC,CAAiB1J,EAAEuJ,OAAOzL,OAC3CgL,UAAU,sGAJZ,GAAA,EAAA,CAAAvD,SAAA,sDAAAC,WAAA,IAAAuD,aAAA,IAAAC,QAMAlI,OAAC,MAAA,CAAIgI,UAAU,6CACbpD,SAAA,CAAA5E,SAAC,IAAA,CAAEgI,UAAU,0BAA0BpD,SAAA,aAAvC,GAAA,EAAA,CAAAH,SAAA,sDAAAC,WAAA,IAAAuD,aAAA,IAAAC,MACAlI,SAAC,IAAA,CAAEgI,UAAU,oBAAqBpD,SAAAmB,QAAlC,GAAA,EAAA,CAAAtB,SAAA,sDAAAC,WAAA,IAAAuD,aAAA,IAAAC,aAFF,GAAA,EAAA,CAAAzD,SAAA,sDAAAC,WAAA,IAAAuD,aAAA,IAAAC,aARF,GAAA,EAAA,CAAAzD,SAAA,sDAAAC,WAAA,IAAAuD,aAAA,IAAAC,aAlBF,GAAA,EAAA,CAAAzD,SAAA,sDAAAC,WAAA,IAAAuD,aAAA,GAAAC,aAvBF,GAAA,EAAA,CAAAzD,SAAA,sDAAAC,WAAA,GAAAuD,aAAA,GAAAC,QA0DAlI,OAAC,SAAA,CAAOgI,UAAU,+BAChBpD,SAAA,CAAA5E,EAAAA,OAAC,KAAE4E,SAAA,+BAAH,GAAA,EAAA,CAAAH,SAAA,sDAAAC,WAAA,IAAAuD,aAAA,GAAAC,MACAlI,SAAC,IAAA,CAAEgI,UAAU,eAAepD,SAAA,qBAA5B,GAAA,EAAA,CAAAH,SAAA,sDAAAC,WAAA,IAAAuD,aAAA,GAAAC,aAFF,GAAA,EAAA,CAAAzD,SAAA,sDAAAC,WAAA,IAAAuD,aAAA,GAAAC,aA1EF,GAAA,EAAA,CAAAzD,SAAA,sDAAAC,WAAA,GAAAuD,aAAA,GAAAC,KAgFJ,CAEA,SAASW,IACP,OACE7I,EAAAA,OAAC/J,EAAM6S,WAAN,CACClE,kBAACkB,EAAA,CAAA,OAAD,GAAA,EAAA,CAAArB,SAAA,sDAAAC,WAAA,IAAAuD,aAAA,GAAAC,YADF,GAAA,EAAA,CAAAzD,SAAA,sDAAAC,WAAA,IAAAuD,aAAA,GAAAC,KAIJ,CCtJAtP,QAAQ2D,IAAI,gBAGZqK,OAAOmC,iBAAiB,QAAUC,IAChCpQ,QAAQtB,MAAM,QAAS0R,EAAM1R,SAG/BsP,OAAOmC,iBAAiB,qBAAuBC,IAC7CpQ,QAAQtB,MAAM,mBAAoB0R,EAAMC,UAI7BC,EAASvD,WAAWsB,SAASkC,eAAe,SACpDvP,gBAAQiP,EAAA,CAAA,OAAD,GAAA,EAAA,CAAApE,SAAA,iDAAAC,WAAA,GAAAuD,aAAA,SAAAC,IAEZtP,QAAQ2D,IAAI", "x_google_ignoreList": [0, 1, 2]}