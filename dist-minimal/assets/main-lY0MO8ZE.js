import{r as e,a as r,g as t}from"./vendor-C_q9xi5L.js";!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))r(e);new MutationObserver(e=>{for(const t of e)if("childList"===t.type)for(const e of t.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&r(e)}).observe(document,{childList:!0,subtree:!0})}function r(e){if(e.ep)return;e.ep=!0;const r=function(e){const r={};return e.integrity&&(r.integrity=e.integrity),e.referrerPolicy&&(r.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?r.credentials="include":"anonymous"===e.crossOrigin?r.credentials="omit":r.credentials="same-origin",r}(e);fetch(e.href,r)}}();var n,i,o={exports:{}},s={};function a(){return n||(n=1,function(){var r=e(),t=Symbol.for("react.element"),n=Symbol.for("react.portal"),i=Symbol.for("react.fragment"),o=Symbol.for("react.strict_mode"),a=Symbol.for("react.profiler"),l=Symbol.for("react.provider"),c=Symbol.for("react.context"),u=Symbol.for("react.forward_ref"),m=Symbol.for("react.suspense"),p=Symbol.for("react.suspense_list"),d=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),v=Symbol.for("react.offscreen"),y=Symbol.iterator;var h=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;function b(e){for(var r=arguments.length,t=new Array(r>1?r-1:0),n=1;n<r;n++)t[n-1]=arguments[n];!function(e,r,t){var n=h.ReactDebugCurrentFrame.getStackAddendum();""!==n&&(r+="%s",t=t.concat([n]));var i=t.map(function(e){return String(e)});i.unshift("Warning: "+r),Function.prototype.apply.call(console[e],console,i)}("error",e,t)}var g;function N(e){return e.displayName||"Context"}function x(e){if(null==e)return null;if("number"==typeof e.tag&&b("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."),"function"==typeof e)return e.displayName||e.name||null;if("string"==typeof e)return e;switch(e){case i:return"Fragment";case n:return"Portal";case a:return"Profiler";case o:return"StrictMode";case m:return"Suspense";case p:return"SuspenseList"}if("object"==typeof e)switch(e.$$typeof){case c:return N(e)+".Consumer";case l:return N(e._context)+".Provider";case u:return function(e,r,t){var n=e.displayName;if(n)return n;var i=r.displayName||r.name||"";return""!==i?t+"("+i+")":t}(e,e.render,"ForwardRef");case d:var r=e.displayName||null;return null!==r?r:x(e.type)||"Memo";case f:var t=e,s=t._payload,v=t._init;try{return x(v(s))}catch(y){return null}}return null}g=Symbol.for("react.module.reference");var S,w,E,j,k,D,_,O=Object.assign,R=0;function U(){}U.__reactDisabledLog=!0;var A,V=h.ReactCurrentDispatcher;function P(e,r,t){if(void 0===A)try{throw Error()}catch(i){var n=i.stack.trim().match(/\n( *(at )?)/);A=n&&n[1]||""}return"\n"+A+e}var T,C=!1,$="function"==typeof WeakMap?WeakMap:Map;function I(e,r){if(!e||C)return"";var t,n=T.get(e);if(void 0!==n)return n;C=!0;var i,o=Error.prepareStackTrace;Error.prepareStackTrace=void 0,i=V.current,V.current=null,function(){if(0===R){S=console.log,w=console.info,E=console.warn,j=console.error,k=console.group,D=console.groupCollapsed,_=console.groupEnd;var e={configurable:!0,enumerable:!0,value:U,writable:!0};Object.defineProperties(console,{info:e,log:e,warn:e,error:e,group:e,groupCollapsed:e,groupEnd:e})}R++}();try{if(r){var s=function(){throw Error()};if(Object.defineProperty(s.prototype,"props",{set:function(){throw Error()}}),"object"==typeof Reflect&&Reflect.construct){try{Reflect.construct(s,[])}catch(f){t=f}Reflect.construct(e,[],s)}else{try{s.call()}catch(f){t=f}e.call(s.prototype)}}else{try{throw Error()}catch(f){t=f}e()}}catch(v){if(v&&t&&"string"==typeof v.stack){for(var a=v.stack.split("\n"),l=t.stack.split("\n"),c=a.length-1,u=l.length-1;c>=1&&u>=0&&a[c]!==l[u];)u--;for(;c>=1&&u>=0;c--,u--)if(a[c]!==l[u]){if(1!==c||1!==u)do{if(c--,--u<0||a[c]!==l[u]){var m="\n"+a[c].replace(" at new "," at ");return e.displayName&&m.includes("<anonymous>")&&(m=m.replace("<anonymous>",e.displayName)),"function"==typeof e&&T.set(e,m),m}}while(c>=1&&u>=0);break}}}finally{C=!1,V.current=i,function(){if(0===--R){var e={configurable:!0,enumerable:!0,writable:!0};Object.defineProperties(console,{log:O({},e,{value:S}),info:O({},e,{value:w}),warn:O({},e,{value:E}),error:O({},e,{value:j}),group:O({},e,{value:k}),groupCollapsed:O({},e,{value:D}),groupEnd:O({},e,{value:_})})}R<0&&b("disabledDepth fell below zero. This is a bug in React. Please file an issue.")}(),Error.prepareStackTrace=o}var p=e?e.displayName||e.name:"",d=p?P(p):"";return"function"==typeof e&&T.set(e,d),d}function L(e,r,t){if(null==e)return"";if("function"==typeof e)return I(e,!(!(n=e.prototype)||!n.isReactComponent));var n;if("string"==typeof e)return P(e);switch(e){case m:return P("Suspense");case p:return P("SuspenseList")}if("object"==typeof e)switch(e.$$typeof){case u:return I(e.render,!1);case d:return L(e.type,r,t);case f:var i=e,o=i._payload,s=i._init;try{return L(s(o),r,t)}catch(a){}}return""}T=new $;var F=Object.prototype.hasOwnProperty,M={},W=h.ReactDebugCurrentFrame;function z(e){if(e){var r=e._owner,t=L(e.type,e._source,r?r.type:null);W.setExtraStackFrame(t)}else W.setExtraStackFrame(null)}var Y=Array.isArray;function B(e){return Y(e)}function K(e){return""+e}function q(e){if(function(e){try{return K(e),!1}catch(r){return!0}}(e))return b("The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.",function(e){return"function"==typeof Symbol&&Symbol.toStringTag&&e[Symbol.toStringTag]||e.constructor.name||"Object"}(e)),K(e)}var J,X,H,Z=h.ReactCurrentOwner,G={key:!0,ref:!0,__self:!0,__source:!0};H={};function Q(e,r,n,i,o){var s,a={},l=null,c=null;for(s in void 0!==n&&(q(n),l=""+n),function(e){if(F.call(e,"key")){var r=Object.getOwnPropertyDescriptor(e,"key").get;if(r&&r.isReactWarning)return!1}return void 0!==e.key}(r)&&(q(r.key),l=""+r.key),function(e){if(F.call(e,"ref")){var r=Object.getOwnPropertyDescriptor(e,"ref").get;if(r&&r.isReactWarning)return!1}return void 0!==e.ref}(r)&&(c=r.ref,function(e,r){if("string"==typeof e.ref&&Z.current&&r&&Z.current.stateNode!==r){var t=x(Z.current.type);H[t]||(b('Component "%s" contains the string ref "%s". Support for string refs will be removed in a future major release. This case cannot be automatically converted to an arrow function. We ask you to manually fix this case by using useRef() or createRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref',x(Z.current.type),e.ref),H[t]=!0)}}(r,o)),r)F.call(r,s)&&!G.hasOwnProperty(s)&&(a[s]=r[s]);if(e&&e.defaultProps){var u=e.defaultProps;for(s in u)void 0===a[s]&&(a[s]=u[s])}if(l||c){var m="function"==typeof e?e.displayName||e.name||"Unknown":e;l&&function(e,r){var t=function(){J||(J=!0,b("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)",r))};t.isReactWarning=!0,Object.defineProperty(e,"key",{get:t,configurable:!0})}(a,m),c&&function(e,r){var t=function(){X||(X=!0,b("%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)",r))};t.isReactWarning=!0,Object.defineProperty(e,"ref",{get:t,configurable:!0})}(a,m)}return function(e,r,n,i,o,s,a){var l={$$typeof:t,type:e,key:r,ref:n,props:a,_owner:s,_store:{}};return Object.defineProperty(l._store,"validated",{configurable:!1,enumerable:!1,writable:!0,value:!1}),Object.defineProperty(l,"_self",{configurable:!1,enumerable:!1,writable:!1,value:i}),Object.defineProperty(l,"_source",{configurable:!1,enumerable:!1,writable:!1,value:o}),Object.freeze&&(Object.freeze(l.props),Object.freeze(l)),l}(e,l,c,o,i,Z.current,a)}var ee,re=h.ReactCurrentOwner,te=h.ReactDebugCurrentFrame;function ne(e){if(e){var r=e._owner,t=L(e.type,e._source,r?r.type:null);te.setExtraStackFrame(t)}else te.setExtraStackFrame(null)}function ie(e){return"object"==typeof e&&null!==e&&e.$$typeof===t}function oe(){if(re.current){var e=x(re.current.type);if(e)return"\n\nCheck the render method of `"+e+"`."}return""}ee=!1;var se={};function ae(e,r){if(e._store&&!e._store.validated&&null==e.key){e._store.validated=!0;var t=function(e){var r=oe();if(!r){var t="string"==typeof e?e:e.displayName||e.name;t&&(r="\n\nCheck the top-level render call using <"+t+">.")}return r}(r);if(!se[t]){se[t]=!0;var n="";e&&e._owner&&e._owner!==re.current&&(n=" It was passed a child from "+x(e._owner.type)+"."),ne(e),b('Each child in a list should have a unique "key" prop.%s%s See https://reactjs.org/link/warning-keys for more information.',t,n),ne(null)}}}function le(e,r){if("object"==typeof e)if(B(e))for(var t=0;t<e.length;t++){var n=e[t];ie(n)&&ae(n,r)}else if(ie(e))e._store&&(e._store.validated=!0);else if(e){var i=function(e){if(null===e||"object"!=typeof e)return null;var r=y&&e[y]||e["@@iterator"];return"function"==typeof r?r:null}(e);if("function"==typeof i&&i!==e.entries)for(var o,s=i.call(e);!(o=s.next()).done;)ie(o.value)&&ae(o.value,r)}}function ce(e){var r,t=e.type;if(null!=t&&"string"!=typeof t){if("function"==typeof t)r=t.propTypes;else{if("object"!=typeof t||t.$$typeof!==u&&t.$$typeof!==d)return;r=t.propTypes}if(r){var n=x(t);!function(e,r,t,n,i){var o=Function.call.bind(F);for(var s in e)if(o(e,s)){var a=void 0;try{if("function"!=typeof e[s]){var l=Error((n||"React class")+": "+t+" type `"+s+"` is invalid; it must be a function, usually from the `prop-types` package, but received `"+typeof e[s]+"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.");throw l.name="Invariant Violation",l}a=e[s](r,s,n,t,null,"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED")}catch(c){a=c}!a||a instanceof Error||(z(i),b("%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).",n||"React class",t,s,typeof a),z(null)),a instanceof Error&&!(a.message in M)&&(M[a.message]=!0,z(i),b("Failed %s type: %s",t,a.message),z(null))}}(r,e.props,"prop",n,e)}else if(void 0!==t.PropTypes&&!ee){ee=!0,b("Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?",x(t)||"Unknown")}"function"!=typeof t.getDefaultProps||t.getDefaultProps.isReactClassApproved||b("getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.")}}var ue={};var me=function(e,r,n,s,y,h){var N=function(e){return"string"==typeof e||"function"==typeof e||e===i||e===a||e===o||e===m||e===p||e===v||"object"==typeof e&&null!==e&&(e.$$typeof===f||e.$$typeof===d||e.$$typeof===l||e.$$typeof===c||e.$$typeof===u||e.$$typeof===g||void 0!==e.getModuleId)}(e);if(!N){var S="";(void 0===e||"object"==typeof e&&null!==e&&0===Object.keys(e).length)&&(S+=" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.");var w,E=function(e){return void 0!==e?"\n\nCheck your code at "+e.fileName.replace(/^.*[\\\/]/,"")+":"+e.lineNumber+".":""}(y);S+=E||oe(),null===e?w="null":B(e)?w="array":void 0!==e&&e.$$typeof===t?(w="<"+(x(e.type)||"Unknown")+" />",S=" Did you accidentally export a JSX literal instead of a component?"):w=typeof e,b("React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s",w,S)}var j=Q(e,r,n,y,h);if(null==j)return j;if(N){var k=r.children;if(void 0!==k)if(s)if(B(k)){for(var D=0;D<k.length;D++)le(k[D],e);Object.freeze&&Object.freeze(k)}else b("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");else le(k,e)}if(F.call(r,"key")){var _=x(e),O=Object.keys(r).filter(function(e){return"key"!==e}),R=O.length>0?"{key: someKey, "+O.join(": ..., ")+": ...}":"{key: someKey}";if(!ue[_+R])b('A props object containing a "key" prop is being spread into JSX:\n  let props = %s;\n  <%s {...props} />\nReact keys must be passed directly to JSX without using spread:\n  let props = %s;\n  <%s key={someKey} {...props} />',R,_,O.length>0?"{"+O.join(": ..., ")+": ...}":"{}",_),ue[_+R]=!0}return e===i?function(e){for(var r=Object.keys(e.props),t=0;t<r.length;t++){var n=r[t];if("children"!==n&&"key"!==n){ne(e),b("Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.",n),ne(null);break}}null!==e.ref&&(ne(e),b("Invalid attribute `ref` supplied to `React.Fragment`."),ne(null))}(j):ce(j),j};s.Fragment=i,s.jsxDEV=me}()),s}var l,c=(i||(i=1,o.exports=a()),o.exports),u={};const m=t(function(){if(l)return u;l=1;var e=r(),t=e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;return u.createRoot=function(r,n){t.usingClientEntryPoint=!0;try{return e.createRoot(r,n)}finally{t.usingClientEntryPoint=!1}},u.hydrateRoot=function(r,n,i){t.usingClientEntryPoint=!0;try{return e.hydrateRoot(r,n,i)}finally{t.usingClientEntryPoint=!1}},u}());var p=e();const d=t(p);function f(){const[e,r]=p.useState(Math.floor(Date.now()/1e3)),[t,n]=p.useState(""),[i,o]=p.useState(!1);p.useEffect(()=>{const e=window.matchMedia("(prefers-color-scheme: dark)").matches;o(e);n((new Date).toISOString().slice(0,16))},[]),p.useEffect(()=>{i?document.documentElement.classList.add("dark"):document.documentElement.classList.remove("dark")},[i]);const s=e=>{try{return new Date(1e3*e).toLocaleString("zh-CN",{timeZone:"Asia/Shanghai",year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})}catch(r){return new Date(1e3*e).toLocaleString()}};return c.jsxDEV("div",{className:"min-h-screen p-4 transition-colors duration-300 "+(i?"bg-gray-900 text-white":"bg-gradient-to-br from-blue-500 to-purple-600 text-white"),children:[c.jsxDEV("header",{className:"text-center mb-8",children:[c.jsxDEV("h1",{className:"text-4xl font-bold mb-2",children:"时间戳转换器"},void 0,!1,{fileName:"/Users/<USER>/dev/tsconv/src/SimpleApp.minimal.tsx",lineNumber:74,columnNumber:9},this),c.jsxDEV("p",{className:"text-lg opacity-90",children:"Unix时间戳与日期时间双向转换工具"},void 0,!1,{fileName:"/Users/<USER>/dev/tsconv/src/SimpleApp.minimal.tsx",lineNumber:75,columnNumber:9},this),c.jsxDEV("button",{onClick:()=>o(!i),className:"mt-4 px-4 py-2 rounded-lg bg-white bg-opacity-20 hover:bg-opacity-30 transition-all duration-200",children:i?"🌞 浅色模式":"🌙 深色模式"},void 0,!1,{fileName:"/Users/<USER>/dev/tsconv/src/SimpleApp.minimal.tsx",lineNumber:76,columnNumber:9},this)]},void 0,!0,{fileName:"/Users/<USER>/dev/tsconv/src/SimpleApp.minimal.tsx",lineNumber:73,columnNumber:7},this),c.jsxDEV("div",{className:"max-w-4xl mx-auto",children:[c.jsxDEV("div",{className:"bg-white bg-opacity-10 backdrop-blur-md rounded-xl p-6 mb-6 border border-white border-opacity-20",children:[c.jsxDEV("h2",{className:"text-xl font-semibold mb-4",children:"当前时间"},void 0,!1,{fileName:"/Users/<USER>/dev/tsconv/src/SimpleApp.minimal.tsx",lineNumber:88,columnNumber:11},this),c.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[c.jsxDEV("div",{children:[c.jsxDEV("p",{className:"text-sm opacity-75 mb-1",children:"时间戳"},void 0,!1,{fileName:"/Users/<USER>/dev/tsconv/src/SimpleApp.minimal.tsx",lineNumber:91,columnNumber:15},this),c.jsxDEV("p",{className:"text-2xl font-mono",children:Math.floor(Date.now()/1e3)},void 0,!1,{fileName:"/Users/<USER>/dev/tsconv/src/SimpleApp.minimal.tsx",lineNumber:92,columnNumber:15},this)]},void 0,!0,{fileName:"/Users/<USER>/dev/tsconv/src/SimpleApp.minimal.tsx",lineNumber:90,columnNumber:13},this),c.jsxDEV("div",{children:[c.jsxDEV("p",{className:"text-sm opacity-75 mb-1",children:"日期时间"},void 0,!1,{fileName:"/Users/<USER>/dev/tsconv/src/SimpleApp.minimal.tsx",lineNumber:95,columnNumber:15},this),c.jsxDEV("p",{className:"text-lg",children:s(Math.floor(Date.now()/1e3))},void 0,!1,{fileName:"/Users/<USER>/dev/tsconv/src/SimpleApp.minimal.tsx",lineNumber:96,columnNumber:15},this)]},void 0,!0,{fileName:"/Users/<USER>/dev/tsconv/src/SimpleApp.minimal.tsx",lineNumber:94,columnNumber:13},this)]},void 0,!0,{fileName:"/Users/<USER>/dev/tsconv/src/SimpleApp.minimal.tsx",lineNumber:89,columnNumber:11},this),c.jsxDEV("button",{onClick:()=>{const e=Math.floor(Date.now()/1e3);r(e),n((new Date).toISOString().slice(0,16))},className:"mt-4 px-6 py-2 bg-blue-500 hover:bg-blue-600 rounded-lg transition-colors duration-200",children:"更新到当前时间"},void 0,!1,{fileName:"/Users/<USER>/dev/tsconv/src/SimpleApp.minimal.tsx",lineNumber:99,columnNumber:11},this)]},void 0,!0,{fileName:"/Users/<USER>/dev/tsconv/src/SimpleApp.minimal.tsx",lineNumber:87,columnNumber:9},this),c.jsxDEV("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[c.jsxDEV("div",{className:"bg-white bg-opacity-10 backdrop-blur-md rounded-xl p-6 border border-white border-opacity-20",children:[c.jsxDEV("h3",{className:"text-lg font-semibold mb-4",children:"时间戳转日期"},void 0,!1,{fileName:"/Users/<USER>/dev/tsconv/src/SimpleApp.minimal.tsx",lineNumber:111,columnNumber:13},this),c.jsxDEV("input",{type:"number",value:e,onChange:e=>(e=>{const t=parseInt(e);if(!isNaN(t)&&t>0){r(t);const e=new Date(1e3*t);n(e.toISOString().slice(0,16))}})(e.target.value),className:"w-full p-3 rounded-lg bg-white bg-opacity-20 border border-white border-opacity-30 placeholder-white placeholder-opacity-70 text-white",placeholder:"输入时间戳"},void 0,!1,{fileName:"/Users/<USER>/dev/tsconv/src/SimpleApp.minimal.tsx",lineNumber:112,columnNumber:13},this),c.jsxDEV("div",{className:"mt-4 p-3 bg-black bg-opacity-20 rounded-lg",children:[c.jsxDEV("p",{className:"text-sm opacity-75 mb-1",children:"转换结果"},void 0,!1,{fileName:"/Users/<USER>/dev/tsconv/src/SimpleApp.minimal.tsx",lineNumber:120,columnNumber:15},this),c.jsxDEV("p",{className:"font-mono",children:s(e)},void 0,!1,{fileName:"/Users/<USER>/dev/tsconv/src/SimpleApp.minimal.tsx",lineNumber:121,columnNumber:15},this)]},void 0,!0,{fileName:"/Users/<USER>/dev/tsconv/src/SimpleApp.minimal.tsx",lineNumber:119,columnNumber:13},this)]},void 0,!0,{fileName:"/Users/<USER>/dev/tsconv/src/SimpleApp.minimal.tsx",lineNumber:110,columnNumber:11},this),c.jsxDEV("div",{className:"bg-white bg-opacity-10 backdrop-blur-md rounded-xl p-6 border border-white border-opacity-20",children:[c.jsxDEV("h3",{className:"text-lg font-semibold mb-4",children:"日期转时间戳"},void 0,!1,{fileName:"/Users/<USER>/dev/tsconv/src/SimpleApp.minimal.tsx",lineNumber:127,columnNumber:13},this),c.jsxDEV("input",{type:"datetime-local",value:t,onChange:e=>(e=>{n(e);const t=new Date(e);isNaN(t.getTime())||r(Math.floor(t.getTime()/1e3))})(e.target.value),className:"w-full p-3 rounded-lg bg-white bg-opacity-20 border border-white border-opacity-30 text-white"},void 0,!1,{fileName:"/Users/<USER>/dev/tsconv/src/SimpleApp.minimal.tsx",lineNumber:128,columnNumber:13},this),c.jsxDEV("div",{className:"mt-4 p-3 bg-black bg-opacity-20 rounded-lg",children:[c.jsxDEV("p",{className:"text-sm opacity-75 mb-1",children:"转换结果"},void 0,!1,{fileName:"/Users/<USER>/dev/tsconv/src/SimpleApp.minimal.tsx",lineNumber:135,columnNumber:15},this),c.jsxDEV("p",{className:"font-mono text-xl",children:e},void 0,!1,{fileName:"/Users/<USER>/dev/tsconv/src/SimpleApp.minimal.tsx",lineNumber:136,columnNumber:15},this)]},void 0,!0,{fileName:"/Users/<USER>/dev/tsconv/src/SimpleApp.minimal.tsx",lineNumber:134,columnNumber:13},this)]},void 0,!0,{fileName:"/Users/<USER>/dev/tsconv/src/SimpleApp.minimal.tsx",lineNumber:126,columnNumber:11},this)]},void 0,!0,{fileName:"/Users/<USER>/dev/tsconv/src/SimpleApp.minimal.tsx",lineNumber:108,columnNumber:9},this)]},void 0,!0,{fileName:"/Users/<USER>/dev/tsconv/src/SimpleApp.minimal.tsx",lineNumber:85,columnNumber:7},this),c.jsxDEV("footer",{className:"text-center mt-12 opacity-75",children:[c.jsxDEV("p",{children:"© 2024 时间戳转换器 - 免费在线工具"},void 0,!1,{fileName:"/Users/<USER>/dev/tsconv/src/SimpleApp.minimal.tsx",lineNumber:144,columnNumber:9},this),c.jsxDEV("p",{className:"text-sm mt-2",children:"简化版本 - 无外部依赖"},void 0,!1,{fileName:"/Users/<USER>/dev/tsconv/src/SimpleApp.minimal.tsx",lineNumber:145,columnNumber:9},this)]},void 0,!0,{fileName:"/Users/<USER>/dev/tsconv/src/SimpleApp.minimal.tsx",lineNumber:143,columnNumber:7},this)]},void 0,!0,{fileName:"/Users/<USER>/dev/tsconv/src/SimpleApp.minimal.tsx",lineNumber:69,columnNumber:5},this)}function v(){return c.jsxDEV(d.StrictMode,{children:c.jsxDEV(f,{},void 0,!1,{fileName:"/Users/<USER>/dev/tsconv/src/SimpleApp.minimal.tsx",lineNumber:154,columnNumber:7},this)},void 0,!1,{fileName:"/Users/<USER>/dev/tsconv/src/SimpleApp.minimal.tsx",lineNumber:153,columnNumber:5},this)}console.log("✅ 简化版本启动中..."),window.addEventListener("error",e=>{console.error("全局错误:",e.error)}),window.addEventListener("unhandledrejection",e=>{console.error("未处理的 Promise 拒绝:",e.reason)});m.createRoot(document.getElementById("root")).render(c.jsxDEV(v,{},void 0,!1,{fileName:"/Users/<USER>/dev/tsconv/src/main.minimal.tsx",lineNumber:20,columnNumber:13},void 0)),console.log("✅ 简化版本启动完成");
//# sourceMappingURL=main-lY0MO8ZE.js.map
