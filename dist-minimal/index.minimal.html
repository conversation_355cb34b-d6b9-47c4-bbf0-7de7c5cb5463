<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>时间戳转换器 - 简化版本</title>
    
    <meta name="description" content="简化版时间戳转换器，无外部依赖，快速稳定的Unix时间戳与日期时间双向转换工具" />
    <meta name="keywords" content="时间戳转换器,timestamp converter,unix时间戳,日期转换,简化版" />
    
    <!-- 基本样式 -->
    <style>
      body {
        margin: 0;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
      }
      #root {
        display: flex;
        flex-direction: column;
        min-height: 100vh;
      }
      .loading {
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 100vh;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        font-size: 18px;
      }
    </style>
    <script type="module" crossorigin src="/assets/main-lY0MO8ZE.js"></script>
    <link rel="modulepreload" crossorigin href="/assets/vendor-C_q9xi5L.js">
    <link rel="stylesheet" crossorigin href="/assets/main-DtQXzP5d.css">
  </head>
  <body>
    <div id="root">
      <div class="loading">
        <div>
          <h2>⏰ 时间戳转换器加载中...</h2>
          <p>简化版本 - 无外部依赖</p>
        </div>
      </div>
    </div>
    
    <script>
      // 基本的错误处理
      window.addEventListener('error', (event) => {
        console.error('页面错误:', event.error);
        document.getElementById('root').innerHTML = `
          <div class="loading">
            <div style="text-align: center; color: #ff6b6b;">
              <h2>❌ 加载失败</h2>
              <p>错误: ${event.error?.message || '未知错误'}</p>
              <button onclick="location.reload()" style="padding: 10px 20px; margin-top: 10px; background: #fff; color: #333; border: none; border-radius: 5px; cursor: pointer;">
                重新加载
              </button>
            </div>
          </div>
        `;
      });
      
      window.addEventListener('unhandledrejection', (event) => {
        console.error('Promise 错误:', event.reason);
      });
    </script>
    
  </body>
</html>
