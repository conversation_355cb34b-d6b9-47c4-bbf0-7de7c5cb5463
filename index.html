<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <!-- Optimized favicons with modern format support -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />

    <!-- Preload critical optimized images -->
    <link rel="preload" as="image" href="/favicon-32x32.png" type="image/png" />
    <link rel="preload" as="image" href="/apple-touch-icon.png" type="image/png" />

    <!-- Optimized favicons -->
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Timestamp Converter - Unix & Epoch Time | tsconv.com</title>

    <!-- Resource hints for critical assets -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />

    <!-- Vite will automatically inject the correct preload links -->

    <!-- Font preloading will be handled by Google Fonts -->

    <!-- Preload Google Fonts stylesheet -->
    <link
      rel="preload"
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600&display=swap"
      as="style"
      onload="this.onload=null;this.rel='stylesheet'"
    />
    <noscript
      ><link
        rel="stylesheet"
        href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600&display=swap"
    /></noscript>
    <meta
      name="description"
      content="Convert Unix timestamps to human-readable dates and vice versa. Fast, simple, and accurate timestamp conversion tool with real-time results."
    />
    <meta
      name="keywords"
      content="timestamp converter, unix timestamp, epoch time, date converter, time conversion, unix time"
    />
    <meta name="author" content="tsconv.com" />

    <!-- Canonical URL -->
    <link rel="canonical" href="https://tsconv.com/" />

    <!-- Sitemap -->
    <link rel="sitemap" type="application/xml" href="/sitemap.xml" />

    <!-- Open Graph -->
    <meta property="og:title" content="Timestamp Converter - Unix & Epoch Time" />
    <meta
      property="og:description"
      content="Convert Unix timestamps to human-readable dates and vice versa. Fast, simple, and accurate timestamp conversion tool."
    />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://tsconv.com" />
    <meta property="og:image" content="https://tsconv.com/tsconv_logo.png" />
    <meta name="twitter:card" content="summary" />
    <meta name="twitter:title" content="Timestamp Converter - Unix & Epoch Time" />
    <meta
      name="twitter:description"
      content="Convert Unix timestamps to human-readable dates and vice versa. Fast, simple, and accurate timestamp conversion tool."
    />
    <meta name="twitter:image" content="https://tsconv.com/tsconv_logo.png" />

    <!-- JSON-LD Structured Data -->
    <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "WebSite",
        "name": "tsconv.com",
        "alternateName": "Timestamp Converter",
        "url": "https://tsconv.com",
        "logo": "https://tsconv.com/tsconv_logo.png",
        "description": "Convert Unix timestamps to human-readable dates and vice versa. Fast, simple, and accurate timestamp conversion tool with real-time results.",
        "publisher": {
          "@type": "Organization",
          "name": "tsconv.com",
          "logo": {
            "@type": "ImageObject",
            "url": "https://tsconv.com/tsconv_logo.png",
            "width": 512,
            "height": 512
          }
        },
        "potentialAction": {
          "@type": "SearchAction",
          "target": "https://tsconv.com/?q={search_term_string}",
          "query-input": "required name=search_term_string"
        }
      }
    </script>
  </head>
  <body>
    <div id="root"></div>

    <!-- Inline critical CSS for above-the-fold content -->
    <style>
      /* Critical CSS for initial render - minimal styles for header and main container */
      body {
        margin: 0;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
      }
      #root {
        display: flex;
        flex-direction: column;
        min-height: 100vh;
      }
      /* Add any other critical styles needed for above-the-fold content */
    </style>

    <!-- Async CSS loading -->
    <script>
      // Function to load CSS asynchronously
      function loadCSS(href) {
        var link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = href;
        link.media = 'all';
        document.head.appendChild(link);
      }

      // Vite will handle CSS and JS loading automatically
    </script>

    <!-- Main application script with high priority -->
    <script type="module" src="/src/main.tsx" fetchpriority="high"></script>
  </body>
</html>
