# Content Security Policy for Cloudflare Pages
/*
  # Security Headers
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  X-XSS-Protection: 1; mode=block
  Referrer-Policy: strict-origin-when-cross-origin
  Permissions-Policy: camera=(), microphone=(), geolocation=(), payment=()

  # Content Security Policy - Enhanced security
  Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: https://static.cloudflareinsights.com https://challenges.cloudflare.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https://tsconv.com https://*.tsconv.com; connect-src 'self' https://api.tsconv.com https://*.upstash.io https://o4507902068293632.ingest.us.sentry.io https://www.google-analytics.com; frame-ancestors 'none'; base-uri 'self'; form-action 'self'; object-src 'none'; media-src 'self'
  # HSTS (HTTP Strict Transport Security)
  Strict-Transport-Security: max-age=31536000; includeSubDomains; preload

  # Cache Control for static assets
  Cache-Control: public, max-age=31536000, immutable

# API specific headers
/api/*
  # CORS Headers
  Access-Control-Allow-Origin: https://tsconv.com
  Access-Control-Allow-Methods: GET, POST, OPTIONS
  Access-Control-Allow-Headers: Content-Type, Authorization, X-API-Key
  Access-Control-Max-Age: 86400

  # API Security
  X-Content-Type-Options: nosniff
  X-Frame-Options: DENY

  # Rate Limiting Headers
  X-RateLimit-Limit: 100
  X-RateLimit-Window: 60

# Static assets caching
/assets/*
  Cache-Control: public, max-age=31536000, immutable

# JavaScript files - ensure correct MIME type
/assets/*.js
  Content-Type: application/javascript; charset=utf-8
  Cache-Control: public, max-age=31536000, immutable

# CSS files
/assets/*.css
  Content-Type: text/css; charset=utf-8
  Cache-Control: public, max-age=31536000, immutable

/favicon.ico
  Cache-Control: public, max-age=86400

# Service Worker
/sw.js
  Cache-Control: no-cache, no-store, must-revalidate

# Manifest
/manifest.json
  Cache-Control: public, max-age=86400
